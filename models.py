# coding:utf-8
from dataclasses import dataclass, field
from typing import List
import uuid
from datetime import datetime

@dataclass
class TodoItem:
    """TODO项数据模型"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    title: str = ""
    description: str = ""
    completed: bool = False
    created_at: datetime = field(default_factory=datetime.now)
    order: int = 0

@dataclass
class IssueItem:
    """Issue项数据模型"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    title: str = ""
    description: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    order: int = 0

@dataclass
class Project:
    """项目数据模型"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    todos: List[TodoItem] = field(default_factory=list)
    issues: List[IssueItem] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    
    def add_todo(self, title: str, description: str = "") -> TodoItem:
        """添加TODO项"""
        order = len(self.todos)
        todo = TodoItem(title=title, description=description, order=order)
        self.todos.append(todo)
        return todo
    
    def remove_todo(self, todo_id: str) -> bool:
        """删除TODO项"""
        for i, todo in enumerate(self.todos):
            if todo.id == todo_id:
                self.todos.pop(i)
                # 重新排序
                for j, remaining_todo in enumerate(self.todos):
                    remaining_todo.order = j
                return True
        return False
    
    def reorder_todos(self, todo_ids: List[str]):
        """重新排序TODO项"""
        todo_dict = {todo.id: todo for todo in self.todos}
        self.todos = []
        for i, todo_id in enumerate(todo_ids):
            if todo_id in todo_dict:
                todo_dict[todo_id].order = i
                self.todos.append(todo_dict[todo_id])
    
    def add_issue(self, title: str, description: str = "") -> IssueItem:
        """添加Issue项"""
        order = len(self.issues)
        issue = IssueItem(title=title, description=description, order=order)
        self.issues.append(issue)
        return issue
    
    def remove_issue(self, issue_id: str) -> bool:
        """删除Issue项"""
        for i, issue in enumerate(self.issues):
            if issue.id == issue_id:
                self.issues.pop(i)
                # 重新排序
                for j, remaining_issue in enumerate(self.issues):
                    remaining_issue.order = j
                return True
        return False
    
    def reorder_issues(self, issue_ids: List[str]):
        """重新排序Issue项"""
        issue_dict = {issue.id: issue for issue in self.issues}
        self.issues = []
        for i, issue_id in enumerate(issue_ids):
            if issue_id in issue_dict:
                issue_dict[issue_id].order = i
                self.issues.append(issue_dict[issue_id])
