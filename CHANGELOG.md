# 更新日志

## [1.4.0] - 2024-06-11

### 新功能
- ✨ TODO智能分离显示功能
- ✨ 未完成和已完成TODO分为上下两个独立列表
- ✨ 实时状态切换：完成状态改变时自动移动到对应列表
- ✨ 独立拖拽排序：两个列表可以分别进行拖拽排序
- ✨ 数量统计显示：标签显示每个列表的项目数量

### 改进
- 🎨 优化TODO面板布局，提升用户体验
- 📊 添加视觉区分：使用不同颜色和图标区分状态
- 🔄 改进状态切换逻辑，确保数据一致性
- 📱 保持响应式设计，适配不同窗口大小

### 技术细节
- 未完成列表标签：📋 未完成 (数量)
- 已完成列表标签：✅ 已完成 (数量)
- 两个列表各占50%空间，可独立滚动
- 状态改变时自动刷新列表显示

### 测试
- ✅ 添加TODO分离功能测试脚本
- ✅ 验证状态切换和拖拽排序逻辑
- ✅ 测试各种边界情况（空列表、单一状态等）

## [1.3.0] - 2024-06-11

### 新功能
- ✨ 智能文本截断功能
- ✨ 长标题自动省略显示（项目名称、TODO标题、Issue标题）
- ✨ 悬停提示显示完整内容
- ✨ 自适应截断长度（不同组件使用不同的最大长度）

### 改进
- 🎨 优化长文本显示体验
- 📱 提升界面整洁度和可读性
- 🔧 添加文本处理工具函数
- 📝 支持项目描述的智能截断

### 技术细节
- 项目名称最大显示30字符
- 项目描述最大显示60字符
- TODO/Issue标题最大显示25字符
- TODO/Issue描述最大显示40字符
- 项目详情页标题最大显示40字符

### 测试
- ✅ 添加文本截断功能测试脚本
- ✅ 验证不同长度文本的截断效果
- ✅ 创建长标题测试数据生成器

## [1.2.0] - 2024-06-11

### 新功能
- ✨ 新增项目删除功能
- ✨ 项目卡片添加删除按钮
- ✨ 删除确认对话框，防止误删
- ✨ 智能返回逻辑（删除当前查看的项目时自动返回列表）

### 改进
- 🎨 优化项目卡片布局，添加操作按钮区域
- 🔧 改进父组件查找逻辑，确保方法调用正确
- 📱 保持界面一致性和用户体验

### 测试
- ✅ 添加项目删除功能测试脚本
- ✅ 验证删除功能的各种边界情况
- ✅ 确保数据持久化正常工作

## [1.1.0] - 2024-06-11

### 修复
- 🔧 修复项目详情页面header_layout过宽的上下边距问题
- 🔧 优化项目列表页面布局，与详情页面保持一致
- 🔧 修复父组件方法调用问题，确保TODO和Issue操作正常
- 🔧 修复项目卡片点击信号冲突问题

### 改进
- ✨ 优化界面布局边距，使界面更加紧凑美观
- ✨ 统一按钮高度，提升界面一致性
- ✨ 改进组件间距设置，提升视觉效果
- ✨ 添加拉伸因子，优化空间利用

### 验证
- ✅ 数据持久化功能完全正常
- ✅ 所有CRUD操作正常工作
- ✅ 拖拽排序功能正常
- ✅ 界面布局优化完成

## [1.0.0] - 2024-06-11

### 新功能
- 🎉 初始版本发布
- ✨ 项目管理功能
- ✨ TODO管理功能（增删改查、完成状态、拖拽排序）
- ✨ Issue管理功能（增删改查、拖拽排序）
- ✨ 数据持久化到JSON文件
- ✨ 现代化Fluent Design界面
- ✨ 双面板布局（TODO + Issue）
- ✨ 响应式设计

### 技术栈
- PyQt6 - GUI框架
- qfluentwidgets - UI组件库
- Python dataclasses - 数据模型
- JSON - 数据存储

### 文件结构
- 完整的模块化设计
- 清晰的代码结构
- 完善的测试脚本
- 详细的文档说明
