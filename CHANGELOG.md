# 更新日志

## [1.1.0] - 2024-06-11

### 修复
- 🔧 修复项目详情页面header_layout过宽的上下边距问题
- 🔧 优化项目列表页面布局，与详情页面保持一致
- 🔧 修复父组件方法调用问题，确保TODO和Issue操作正常
- 🔧 修复项目卡片点击信号冲突问题

### 改进
- ✨ 优化界面布局边距，使界面更加紧凑美观
- ✨ 统一按钮高度，提升界面一致性
- ✨ 改进组件间距设置，提升视觉效果
- ✨ 添加拉伸因子，优化空间利用

### 验证
- ✅ 数据持久化功能完全正常
- ✅ 所有CRUD操作正常工作
- ✅ 拖拽排序功能正常
- ✅ 界面布局优化完成

## [1.0.0] - 2024-06-11

### 新功能
- 🎉 初始版本发布
- ✨ 项目管理功能
- ✨ TODO管理功能（增删改查、完成状态、拖拽排序）
- ✨ Issue管理功能（增删改查、拖拽排序）
- ✨ 数据持久化到JSON文件
- ✨ 现代化Fluent Design界面
- ✨ 双面板布局（TODO + Issue）
- ✨ 响应式设计

### 技术栈
- PyQt6 - GUI框架
- qfluentwidgets - UI组件库
- Python dataclasses - 数据模型
- JSON - 数据存储

### 文件结构
- 完整的模块化设计
- 清晰的代码结构
- 完善的测试脚本
- 详细的文档说明
