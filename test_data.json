{"projects": [{"id": "ded99f59-9a1f-4232-b7d9-6e4b7c07131b", "name": "网站开发项目", "description": "开发公司官方网站", "created_at": "2025-06-11T08:44:32.922779", "todos": [{"id": "b54c94a4-939a-4d9d-b05b-ca0a38d84790", "title": "设计首页布局", "description": "创建响应式首页设计", "completed": false, "created_at": "2025-06-11T08:44:32.922779", "order": 0}, {"id": "7fd1c560-4a3d-48c4-9e34-f56b3a8e4feb", "title": "实现用户登录", "description": "添加用户认证功能", "completed": false, "created_at": "2025-06-11T08:44:32.922779", "order": 1}, {"id": "4cf5d2dc-0ac3-495d-ba0a-f5d3ac4b88e1", "title": "优化页面性能", "description": "提升页面加载速度", "completed": false, "created_at": "2025-06-11T08:44:32.922779", "order": 2}], "issues": [{"id": "6c3160bb-605a-4ca7-83fe-923e7f3c3d9d", "title": "登录按钮样式问题", "description": "登录按钮在移动端显示异常", "created_at": "2025-06-11T08:44:32.922779", "order": 0}, {"id": "8eb8559e-9716-49e0-9c91-beec84497010", "title": "数据库连接超时", "description": "在高并发情况下数据库连接超时", "created_at": "2025-06-11T08:44:32.922779", "order": 1}]}, {"id": "bb9ea3a5-40b1-4cb9-aea8-1bfd5ffdb698", "name": "移动应用开发", "description": "开发iOS和Android应用", "created_at": "2025-06-11T08:44:32.922779", "todos": [{"id": "ad7da8f4-d9e8-4d53-89ab-2deae3c23ad3", "title": "完成UI设计", "description": "设计应用界面和交互", "completed": false, "created_at": "2025-06-11T08:44:32.922779", "order": 0}, {"id": "0ee12020-ee39-4418-be61-432b67ab466d", "title": "实现核心功能", "description": "开发主要业务逻辑", "completed": false, "created_at": "2025-06-11T08:44:32.922779", "order": 1}], "issues": [{"id": "a2ebab55-47d7-4aaa-b4bf-a9bba7273ddc", "title": "iOS适配问题", "description": "应用在某些iOS版本上崩溃", "created_at": "2025-06-11T08:44:32.922779", "order": 0}]}, {"id": "b4c252ec-c989-441c-81f3-4ef2c038012a", "name": "新测试项目", "description": "这是通过数据管理器创建的项目", "created_at": "2025-06-11T08:44:32.923776", "todos": [{"id": "382b6bc9-4acc-4c42-bd20-d1243bcca192", "title": "测试任务", "description": "这是一个测试任务", "completed": false, "created_at": "2025-06-11T08:44:32.924777", "order": 0}], "issues": []}]}