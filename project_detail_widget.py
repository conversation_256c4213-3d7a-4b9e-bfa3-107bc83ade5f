# coding:utf-8
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QSplitter
from PyQt6.QtCore import Qt, pyqtSignal
from qfluentwidgets import (PushButton, TitleLabel, FluentIcon as FIF)
from models import Project
from todo_panel import TodoPanel
from issue_panel import IssuePanel
from text_utils import get_display_text_and_tooltip

class ProjectDetailWidget(QWidget):
    """项目详情页面"""
    back_requested = pyqtSignal()  # 返回信号
    project_updated = pyqtSignal(Project)  # 项目更新信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_project: Project = None
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 10, 20, 20)  # 减少顶部边距
        layout.setSpacing(10)  # 设置组件间距

        # 顶部导航栏
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)  # 移除header内边距
        header_layout.setSpacing(15)  # 设置header内组件间距

        # 返回按钮
        self.back_button = PushButton(FIF.LEFT_ARROW, "返回")
        self.back_button.clicked.connect(self.back_requested.emit)
        self.back_button.setFixedHeight(32)  # 固定按钮高度

        # 项目标题
        self.project_title = TitleLabel("项目详情")

        header_layout.addWidget(self.back_button)
        header_layout.addWidget(self.project_title)
        header_layout.addStretch()

        # 分割器 - 左侧TODO，右侧Issue
        self.splitter = QSplitter(Qt.Orientation.Horizontal)

        # TODO面板
        self.todo_panel = TodoPanel()

        # Issue面板
        self.issue_panel = IssuePanel()

        self.splitter.addWidget(self.todo_panel)
        self.splitter.addWidget(self.issue_panel)
        self.splitter.setSizes([400, 400])  # 设置初始大小

        layout.addLayout(header_layout)
        layout.addWidget(self.splitter, 1)  # 给splitter设置拉伸因子
        
    def set_project(self, project: Project):
        """设置当前项目"""
        self.current_project = project

        # 设置项目标题，如果过长则截断并添加提示
        title_text = f"{project.name} - 项目详情"
        title_display, title_tooltip = get_display_text_and_tooltip(title_text, 40)
        self.project_title.setText(title_display)
        if title_tooltip:
            self.project_title.setToolTip(f"项目: {project.name}")

        # 设置TODO和Issue数据
        self.todo_panel.set_todos(project.todos)
        self.issue_panel.set_issues(project.issues)
        
    # TODO相关方法
    def add_todo_item(self, title: str, description: str):
        """添加TODO项"""
        if self.current_project:
            todo = self.current_project.add_todo(title, description)
            self.todo_panel.set_todos(self.current_project.todos)
            self.project_updated.emit(self.current_project)
            
    def update_todo_item(self, todo_id: str, title: str, description: str):
        """更新TODO项"""
        if self.current_project:
            for todo in self.current_project.todos:
                if todo.id == todo_id:
                    todo.title = title
                    todo.description = description
                    break
            self.todo_panel.set_todos(self.current_project.todos)
            self.project_updated.emit(self.current_project)
            
    def delete_todo_item(self, todo_id: str):
        """删除TODO项"""
        if self.current_project:
            self.current_project.remove_todo(todo_id)
            self.todo_panel.set_todos(self.current_project.todos)
            self.project_updated.emit(self.current_project)
            
    def update_todo_completed(self, todo_id: str, completed: bool):
        """更新TODO完成状态"""
        if self.current_project:
            for todo in self.current_project.todos:
                if todo.id == todo_id:
                    todo.completed = completed
                    break
            self.project_updated.emit(self.current_project)
            
    def reorder_todo_items(self, todo_ids: list):
        """重新排序TODO项"""
        if self.current_project:
            self.current_project.reorder_todos(todo_ids)
            self.project_updated.emit(self.current_project)
            
    # Issue相关方法
    def add_issue_item(self, title: str, description: str):
        """添加Issue项"""
        if self.current_project:
            issue = self.current_project.add_issue(title, description)
            self.issue_panel.set_issues(self.current_project.issues)
            self.project_updated.emit(self.current_project)
            
    def update_issue_item(self, issue_id: str, title: str, description: str):
        """更新Issue项"""
        if self.current_project:
            for issue in self.current_project.issues:
                if issue.id == issue_id:
                    issue.title = title
                    issue.description = description
                    break
            self.issue_panel.set_issues(self.current_project.issues)
            self.project_updated.emit(self.current_project)
            
    def delete_issue_item(self, issue_id: str):
        """删除Issue项"""
        if self.current_project:
            self.current_project.remove_issue(issue_id)
            self.issue_panel.set_issues(self.current_project.issues)
            self.project_updated.emit(self.current_project)
            
    def reorder_issue_items(self, issue_ids: list):
        """重新排序Issue项"""
        if self.current_project:
            self.current_project.reorder_issues(issue_ids)
            self.project_updated.emit(self.current_project)
