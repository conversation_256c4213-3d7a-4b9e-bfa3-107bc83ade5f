# coding:utf-8
"""
测试文本截断功能
创建包含长标题的测试数据，验证截断和提示功能
"""

import os
from data_manager import DataManager
from text_utils import truncate_text, should_show_tooltip, get_display_text_and_tooltip

def test_text_utils():
    """测试文本工具函数"""
    print("=== 测试文本工具函数 ===")
    
    # 测试短文本
    short_text = "短标题"
    print(f"短文本: '{short_text}'")
    print(f"  截断结果: '{truncate_text(short_text, 10)}'")
    print(f"  需要提示: {should_show_tooltip(short_text, 10)}")
    display, tooltip = get_display_text_and_tooltip(short_text, 10)
    print(f"  显示文本: '{display}', 提示: '{tooltip}'")
    print()
    
    # 测试长文本
    long_text = "这是一个非常非常长的标题，用来测试文本截断功能是否正常工作"
    print(f"长文本: '{long_text}'")
    print(f"  截断结果: '{truncate_text(long_text, 20)}'")
    print(f"  需要提示: {should_show_tooltip(long_text, 20)}")
    display, tooltip = get_display_text_and_tooltip(long_text, 20)
    print(f"  显示文本: '{display}', 提示: '{tooltip}'")
    print()
    
    # 测试空文本
    empty_text = ""
    print(f"空文本: '{empty_text}'")
    print(f"  截断结果: '{truncate_text(empty_text, 10)}'")
    print(f"  需要提示: {should_show_tooltip(empty_text, 10)}")
    display, tooltip = get_display_text_and_tooltip(empty_text, 10)
    print(f"  显示文本: '{display}', 提示: '{tooltip}'")
    print()
    
    # 测试边界情况
    boundary_text = "正好二十个字符的标题测试"  # 20个字符
    print(f"边界文本(20字符): '{boundary_text}'")
    print(f"  截断结果(20): '{truncate_text(boundary_text, 20)}'")
    print(f"  截断结果(19): '{truncate_text(boundary_text, 19)}'")
    print(f"  需要提示(20): {should_show_tooltip(boundary_text, 20)}")
    print(f"  需要提示(19): {should_show_tooltip(boundary_text, 19)}")
    print()

def create_test_data_with_long_titles():
    """创建包含长标题的测试数据"""
    print("=== 创建长标题测试数据 ===")
    
    test_file = "test_long_titles.json"
    
    # 清理之前的测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
    
    dm = DataManager(test_file)
    
    # 创建包含长标题的项目
    long_project_name = "这是一个非常非常长的项目名称，用来测试项目名称过长时的显示效果和悬停提示功能"
    long_project_desc = "这是一个非常详细的项目描述，包含了大量的信息来说明这个项目的目标、范围、技术栈、团队成员、时间计划等等内容，用来测试描述过长时的处理"
    
    project = dm.add_project(long_project_name, long_project_desc)
    
    # 添加长标题的TODO项
    long_todo_titles = [
        "实现用户认证系统，包括登录、注册、密码重置、邮箱验证、双因子认证等完整功能",
        "开发数据可视化仪表板，支持多种图表类型、实时数据更新、交互式操作和自定义配置",
        "构建微服务架构，包括API网关、服务发现、负载均衡、熔断器、监控告警等组件",
        "优化数据库性能，包括索引优化、查询优化、分库分表、读写分离、缓存策略等",
        "集成第三方支付系统，支持支付宝、微信支付、银联支付、PayPal等多种支付方式"
    ]
    
    long_todo_descriptions = [
        "需要实现完整的用户认证流程，包括但不限于：用户注册时的邮箱验证、手机号验证、密码强度检查、登录时的验证码、记住登录状态、自动登录、密码重置功能、双因子认证、OAuth第三方登录等功能",
        "开发一个功能强大的数据可视化仪表板，支持柱状图、折线图、饼图、散点图、热力图、地图等多种图表类型，实现实时数据更新、用户交互操作、自定义配置、数据导出等功能",
        "设计和实现微服务架构，包括服务拆分、API设计、服务间通信、数据一致性、事务管理、错误处理、监控日志、性能优化等方面的考虑",
        "全面优化数据库性能，包括SQL查询优化、索引设计、表结构优化、分库分表策略、读写分离、缓存机制、连接池配置、慢查询分析等",
        "集成多种第三方支付系统，实现统一的支付接口，支持同步和异步通知、订单管理、退款处理、对账功能、风险控制等完整的支付流程"
    ]
    
    for i, (title, desc) in enumerate(zip(long_todo_titles, long_todo_descriptions)):
        project.add_todo(title, desc)
    
    # 添加长标题的Issue项
    long_issue_titles = [
        "用户在使用移动端应用时遇到的界面适配问题，包括不同屏幕尺寸、分辨率、操作系统版本的兼容性",
        "系统在高并发情况下出现的性能瓶颈问题，包括响应时间过长、内存泄漏、CPU占用过高等",
        "数据同步过程中出现的一致性问题，导致不同服务之间的数据不一致，影响业务逻辑",
        "第三方API集成时遇到的稳定性问题，包括接口超时、返回数据格式变化、服务不可用等",
        "安全漏洞修复，包括SQL注入、XSS攻击、CSRF攻击、权限绕过、敏感信息泄露等安全问题"
    ]
    
    long_issue_descriptions = [
        "在不同的移动设备上测试发现界面显示异常，包括按钮位置偏移、文字显示不全、图片变形、布局错乱等问题，需要进行全面的适配优化",
        "当系统并发用户数超过1000时，响应时间明显增加，部分功能出现超时错误，需要进行性能分析和优化，包括代码优化、架构调整、资源扩容等",
        "在分布式环境下，不同服务之间的数据同步出现延迟和不一致问题，导致用户看到的数据不准确，需要设计更好的数据一致性方案",
        "集成的第三方API服务不稳定，经常出现超时、错误响应、服务中断等问题，影响系统的正常运行，需要增加重试机制和降级方案",
        "安全测试发现多个安全漏洞，包括用户输入验证不足、权限控制缺陷、敏感数据传输未加密等问题，需要立即修复以确保系统安全"
    ]
    
    for i, (title, desc) in enumerate(zip(long_issue_titles, long_issue_descriptions)):
        project.add_issue(title, desc)
    
    # 更新项目
    dm.update_project(project)
    
    print(f"✅ 创建了包含长标题的测试项目")
    print(f"   项目名称长度: {len(project.name)} 字符")
    print(f"   项目描述长度: {len(project.description)} 字符")
    print(f"   TODO数量: {len(project.todos)}")
    print(f"   Issue数量: {len(project.issues)}")
    print()
    
    # 显示截断效果
    print("📋 标题截断效果预览：")
    print()
    
    # 项目标题
    name_display, name_tooltip = get_display_text_and_tooltip(project.name, 30)
    desc_display, desc_tooltip = get_display_text_and_tooltip(project.description, 60)
    print(f"项目名称:")
    print(f"  原始: {project.name}")
    print(f"  显示: {name_display}")
    print(f"  提示: {'是' if name_tooltip else '否'}")
    print()
    
    print(f"项目描述:")
    print(f"  原始: {project.description}")
    print(f"  显示: {desc_display}")
    print(f"  提示: {'是' if desc_tooltip else '否'}")
    print()
    
    # TODO标题
    print("TODO标题:")
    for i, todo in enumerate(project.todos[:3]):  # 只显示前3个
        title_display, title_tooltip = get_display_text_and_tooltip(todo.title, 25)
        print(f"  TODO{i+1}:")
        print(f"    原始: {todo.title}")
        print(f"    显示: {title_display}")
        print(f"    提示: {'是' if title_tooltip else '否'}")
    print()
    
    # Issue标题
    print("Issue标题:")
    for i, issue in enumerate(project.issues[:3]):  # 只显示前3个
        title_display, title_tooltip = get_display_text_and_tooltip(issue.title, 25)
        print(f"  Issue{i+1}:")
        print(f"    原始: {issue.title}")
        print(f"    显示: {title_display}")
        print(f"    提示: {'是' if title_tooltip else '否'}")
    print()
    
    print("💡 使用说明：")
    print("   1. 运行 'python main.py' 启动应用")
    print("   2. 在项目列表中可以看到长标题被截断显示")
    print("   3. 将鼠标悬停在截断的标题上可以看到完整内容")
    print("   4. 进入项目详情页面查看TODO和Issue的截断效果")
    print()
    
    # 清理测试文件
    print("🧹 清理测试文件...")
    if os.path.exists(test_file):
        os.remove(test_file)
        print("✅ 测试文件已清理")
    
    return project

def test_different_lengths():
    """测试不同长度的文本截断效果"""
    print("=== 测试不同长度的截断效果 ===")
    
    test_texts = [
        "短",
        "中等长度标题",
        "这是一个比较长的标题，用来测试截断",
        "这是一个非常非常长的标题，包含了大量的文字内容，用来测试文本截断功能在极端情况下的表现",
        "超级超级超级超级超级超级超级超级超级超级超级超级超级超级超级超级超级超级超级超级长的标题"
    ]
    
    max_lengths = [10, 20, 30, 50]
    
    for max_len in max_lengths:
        print(f"\n最大长度: {max_len}")
        print("-" * 60)
        for i, text in enumerate(test_texts):
            display, tooltip = get_display_text_and_tooltip(text, max_len)
            print(f"文本{i+1}: {text[:20]}{'...' if len(text) > 20 else ''}")
            print(f"  显示: {display}")
            print(f"  提示: {'有' if tooltip else '无'}")
            print()

if __name__ == "__main__":
    print("开始测试文本截断功能...")
    print("=" * 60)
    
    try:
        # 测试工具函数
        test_text_utils()
        
        # 测试不同长度
        test_different_lengths()
        
        # 创建测试数据
        create_test_data_with_long_titles()
        
        print("=" * 60)
        print("文本截断功能测试完成! ✅")
        print("\n现在可以运行 'python main.py' 查看实际效果")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        print("测试失败! ❌")
