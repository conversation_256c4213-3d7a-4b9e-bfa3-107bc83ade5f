# coding:utf-8
from PyQt6.QtWidgets import Q<PERSON>ain<PERSON>indow, QStackedWidget
from PyQt6.QtCore import Qt
from qfluentwidgets import setTheme, Theme, FluentIcon as FIF
from data_manager import DataManager
from project_list_widget import ProjectListWidget
from project_detail_widget import ProjectDetailWidget

class TodoApp(QMainWindow):
    """TODO应用主窗口"""
    
    def __init__(self):
        super().__init__()
        self.data_manager = DataManager()
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("TODO项目管理器")
        self.setMinimumSize(1000, 700)
        
        # 设置主题
        setTheme(Theme.LIGHT)
        
        # 创建堆叠窗口
        self.stacked_widget = QStackedWidget()
        self.setCentralWidget(self.stacked_widget)
        
        # 创建页面
        self.project_list_widget = ProjectListWidget()
        self.project_detail_widget = ProjectDetailWidget()
        
        # 添加页面到堆叠窗口
        self.stacked_widget.addWidget(self.project_list_widget)
        self.stacked_widget.addWidget(self.project_detail_widget)
        
        # 初始显示项目列表
        self.show_project_list()
        
    def setup_connections(self):
        """设置信号连接"""
        # 项目列表页面信号
        self.project_list_widget.project_selected.connect(self.show_project_detail)
        
        # 项目详情页面信号
        self.project_detail_widget.back_requested.connect(self.show_project_list)
        self.project_detail_widget.project_updated.connect(self.on_project_updated)
        
    def show_project_list(self):
        """显示项目列表页面"""
        projects = self.data_manager.get_projects()
        self.project_list_widget.set_projects(projects)
        self.stacked_widget.setCurrentWidget(self.project_list_widget)
        
    def show_project_detail(self, project_id: str):
        """显示项目详情页面"""
        project = self.data_manager.get_project(project_id)
        if project:
            self.project_detail_widget.set_project(project)
            self.stacked_widget.setCurrentWidget(self.project_detail_widget)
            
    def add_new_project(self, name: str, description: str):
        """添加新项目"""
        project = self.data_manager.add_project(name, description)
        self.show_project_list()  # 刷新项目列表
        
    def on_project_updated(self, project):
        """项目更新时的处理"""
        self.data_manager.update_project(project)
