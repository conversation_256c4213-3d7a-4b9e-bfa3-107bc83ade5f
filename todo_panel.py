# coding:utf-8
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QScrollArea, QListWidget, QListWidgetItem, QCheckBox)
from PyQt6.QtCore import Qt, pyqtSignal, QMimeData
from PyQt6.QtGui import QDrag
from qfluentwidgets import (PrimaryPushButton, PushButton, CardWidget, BodyLabel,
                           SubtitleLabel, FluentIcon as FIF, MessageBox, LineEdit,
                           TextEdit, CheckBox, ToolButton)
from models import TodoItem
from text_utils import get_display_text_and_tooltip
from typing import List

class TodoItemWidget(CardWidget):
    """TODO项组件"""
    completed_changed = pyqtSignal(str, bool)  # todo_id, completed
    edit_requested = pyqtSignal(str)  # todo_id
    delete_requested = pyqtSignal(str)  # todo_id
    
    def __init__(self, todo_item: TodoItem, parent=None):
        super().__init__(parent)
        self.todo_item = todo_item
        self.setFixedHeight(80)
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)
        
        # 完成状态复选框
        self.checkbox = CheckBox()
        self.checkbox.setChecked(self.todo_item.completed)
        self.checkbox.toggled.connect(self.on_completed_changed)
        
        # 内容区域
        content_layout = QVBoxLayout()
        
        # 标题
        title_display, title_tooltip = get_display_text_and_tooltip(self.todo_item.title, 25)
        self.title_label = SubtitleLabel(title_display)
        if title_tooltip:
            self.title_label.setToolTip(title_tooltip)
        if self.todo_item.completed:
            self.title_label.setStyleSheet("text-decoration: line-through; color: #888;")

        # 描述
        desc_display, desc_tooltip = get_display_text_and_tooltip(self.todo_item.description, 40)
        self.desc_label = BodyLabel(desc_display)
        self.desc_label.setWordWrap(True)
        if desc_tooltip:
            self.desc_label.setToolTip(desc_tooltip)
        if self.todo_item.completed:
            self.desc_label.setStyleSheet("color: #888;")
        
        content_layout.addWidget(self.title_label)
        if self.todo_item.description:
            content_layout.addWidget(self.desc_label)
        
        # 操作按钮
        button_layout = QVBoxLayout()
        
        self.edit_button = ToolButton(FIF.EDIT)
        self.edit_button.setFixedSize(32, 32)
        self.edit_button.clicked.connect(lambda: self.edit_requested.emit(self.todo_item.id))
        
        self.delete_button = ToolButton(FIF.DELETE)
        self.delete_button.setFixedSize(32, 32)
        self.delete_button.clicked.connect(lambda: self.delete_requested.emit(self.todo_item.id))
        
        button_layout.addWidget(self.edit_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addStretch()
        
        layout.addWidget(self.checkbox)
        layout.addLayout(content_layout, 1)
        layout.addLayout(button_layout)
        
    def on_completed_changed(self, checked: bool):
        """完成状态改变"""
        self.completed_changed.emit(self.todo_item.id, checked)
        
        # 更新样式
        if checked:
            self.title_label.setStyleSheet("text-decoration: line-through; color: #888;")
            self.desc_label.setStyleSheet("color: #888;")
        else:
            self.title_label.setStyleSheet("")
            self.desc_label.setStyleSheet("")
    
    def update_todo(self, todo_item: TodoItem):
        """更新TODO项"""
        self.todo_item = todo_item

        # 更新标题
        title_display, title_tooltip = get_display_text_and_tooltip(todo_item.title, 25)
        self.title_label.setText(title_display)
        self.title_label.setToolTip(title_tooltip if title_tooltip else "")

        # 更新描述
        desc_display, desc_tooltip = get_display_text_and_tooltip(todo_item.description, 40)
        self.desc_label.setText(desc_display)
        self.desc_label.setToolTip(desc_tooltip if desc_tooltip else "")

        self.checkbox.setChecked(todo_item.completed)

class DraggableListWidget(QListWidget):
    """支持拖拽的列表组件"""
    items_reordered = pyqtSignal(list)  # 发送重新排序后的item_ids
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDragDropMode(QListWidget.DragDropMode.InternalMove)
        self.setDefaultDropAction(Qt.DropAction.MoveAction)
        
    def dropEvent(self, event):
        """拖拽放置事件"""
        super().dropEvent(event)
        # 获取重新排序后的item_ids
        item_ids = []
        for i in range(self.count()):
            item = self.item(i)
            if item and hasattr(item, 'todo_id'):
                item_ids.append(item.todo_id)
        self.items_reordered.emit(item_ids)

class TodoEditDialog(MessageBox):
    """TODO编辑对话框"""
    
    def __init__(self, todo_item: TodoItem = None, parent=None):
        title = "编辑TODO" if todo_item else "添加TODO"
        super().__init__(title, "请输入TODO信息", parent)
        self.todo_item = todo_item
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        # 标题输入
        self.title_edit = LineEdit()
        self.title_edit.setPlaceholderText("TODO标题")
        if self.todo_item:
            self.title_edit.setText(self.todo_item.title)
        
        # 描述输入
        self.desc_edit = TextEdit()
        self.desc_edit.setPlaceholderText("TODO描述（可选）")
        self.desc_edit.setFixedHeight(80)
        if self.todo_item:
            self.desc_edit.setPlainText(self.todo_item.description)
        
        # 添加到对话框
        self.textLayout.addWidget(QLabel("标题:"))
        self.textLayout.addWidget(self.title_edit)
        self.textLayout.addWidget(QLabel("描述:"))
        self.textLayout.addWidget(self.desc_edit)
        
    def get_todo_info(self):
        """获取TODO信息"""
        return self.title_edit.text().strip(), self.desc_edit.toPlainText().strip()

class TodoPanel(QWidget):
    """TODO面板"""
    todo_updated = pyqtSignal()  # TODO更新信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.todos: List[TodoItem] = []
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)

        # 标题和添加按钮
        header_layout = QHBoxLayout()

        self.title_label = SubtitleLabel("TODO列表")
        self.add_button = PrimaryPushButton(FIF.ADD, "添加TODO")
        self.add_button.clicked.connect(self.add_todo)

        header_layout.addWidget(self.title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.add_button)

        # 未完成TODO列表
        self.pending_label = BodyLabel("📋 未完成")
        self.pending_label.setStyleSheet("font-weight: bold; color: #0078d4;")
        self.pending_list_widget = DraggableListWidget()
        self.pending_list_widget.items_reordered.connect(self.reorder_pending_todos)

        # 已完成TODO列表
        self.completed_label = BodyLabel("✅ 已完成")
        self.completed_label.setStyleSheet("font-weight: bold; color: #107c10;")
        self.completed_list_widget = DraggableListWidget()
        self.completed_list_widget.items_reordered.connect(self.reorder_completed_todos)

        layout.addLayout(header_layout)
        layout.addWidget(self.pending_label)
        layout.addWidget(self.pending_list_widget, 1)  # 给未完成列表更多空间
        layout.addWidget(self.completed_label)
        layout.addWidget(self.completed_list_widget, 1)  # 给已完成列表相同空间
        
    def set_todos(self, todos: List[TodoItem]):
        """设置TODO列表"""
        self.todos = sorted(todos, key=lambda x: x.order)
        self.refresh_todos()
        
    def refresh_todos(self):
        """刷新TODO列表显示"""
        # 清空两个列表
        self.pending_list_widget.clear()
        self.completed_list_widget.clear()

        # 分离未完成和已完成的TODO
        pending_todos = [todo for todo in self.todos if not todo.completed]
        completed_todos = [todo for todo in self.todos if todo.completed]

        # 按顺序排序
        pending_todos.sort(key=lambda x: x.order)
        completed_todos.sort(key=lambda x: x.order)

        # 添加未完成的TODO到上方列表
        for todo in pending_todos:
            self._add_todo_to_list(todo, self.pending_list_widget)

        # 添加已完成的TODO到下方列表
        for todo in completed_todos:
            self._add_todo_to_list(todo, self.completed_list_widget)

        # 更新标签显示数量
        self.pending_label.setText(f"📋 未完成 ({len(pending_todos)})")
        self.completed_label.setText(f"✅ 已完成 ({len(completed_todos)})")

    def _add_todo_to_list(self, todo: TodoItem, list_widget: DraggableListWidget):
        """添加TODO项到指定列表"""
        item = QListWidgetItem()
        item.todo_id = todo.id
        item.setSizeHint(TodoItemWidget(todo).sizeHint())

        widget = TodoItemWidget(todo)
        widget.completed_changed.connect(self.on_todo_completed_changed)
        widget.edit_requested.connect(self.edit_todo)
        widget.delete_requested.connect(self.delete_todo)

        list_widget.addItem(item)
        list_widget.setItemWidget(item, widget)
            
    def add_todo(self):
        """添加TODO"""
        dialog = TodoEditDialog(parent=self)
        if dialog.exec():
            title, description = dialog.get_todo_info()
            if title:
                # 通知父组件添加TODO
                parent = self.parent()
                while parent and not hasattr(parent, 'add_todo_item'):
                    parent = parent.parent()
                if parent:
                    parent.add_todo_item(title, description)
            else:
                MessageBox("错误", "TODO标题不能为空", self).exec()

    def edit_todo(self, todo_id: str):
        """编辑TODO"""
        todo = next((t for t in self.todos if t.id == todo_id), None)
        if todo:
            dialog = TodoEditDialog(todo, self)
            if dialog.exec():
                title, description = dialog.get_todo_info()
                if title:
                    # 通知父组件更新TODO
                    parent = self.parent()
                    while parent and not hasattr(parent, 'update_todo_item'):
                        parent = parent.parent()
                    if parent:
                        parent.update_todo_item(todo_id, title, description)
                else:
                    MessageBox("错误", "TODO标题不能为空", self).exec()

    def delete_todo(self, todo_id: str):
        """删除TODO"""
        reply = MessageBox("确认删除", "确定要删除这个TODO吗？", self)
        if reply.exec():
            # 通知父组件删除TODO
            parent = self.parent()
            while parent and not hasattr(parent, 'delete_todo_item'):
                parent = parent.parent()
            if parent:
                parent.delete_todo_item(todo_id)
            
    def on_todo_completed_changed(self, todo_id: str, completed: bool):
        """TODO完成状态改变"""
        # 通知父组件更新TODO状态
        parent = self.parent()
        while parent and not hasattr(parent, 'update_todo_completed'):
            parent = parent.parent()
        if parent:
            parent.update_todo_completed(todo_id, completed)

        # 状态改变后重新刷新列表，将TODO移动到正确的列表中
        self.refresh_todos()

    def reorder_pending_todos(self, todo_ids: List[str]):
        """重新排序未完成的TODO"""
        # 只处理未完成的TODO重新排序
        pending_todos = [todo for todo in self.todos if not todo.completed]
        self._reorder_todos_by_ids(pending_todos, todo_ids)

    def reorder_completed_todos(self, todo_ids: List[str]):
        """重新排序已完成的TODO"""
        # 只处理已完成的TODO重新排序
        completed_todos = [todo for todo in self.todos if todo.completed]
        self._reorder_todos_by_ids(completed_todos, todo_ids)

    def _reorder_todos_by_ids(self, todos_subset: List[TodoItem], todo_ids: List[str]):
        """根据ID列表重新排序TODO子集"""
        if not todo_ids:
            return

        # 创建ID到TODO的映射
        todo_dict = {todo.id: todo for todo in todos_subset}

        # 重新分配order值
        for i, todo_id in enumerate(todo_ids):
            if todo_id in todo_dict:
                todo_dict[todo_id].order = i

        # 通知父组件更新整个TODO列表
        parent = self.parent()
        while parent and not hasattr(parent, 'reorder_todo_items'):
            parent = parent.parent()
        if parent:
            # 获取所有TODO的ID列表，保持原有的排序逻辑
            all_todo_ids = [todo.id for todo in sorted(self.todos, key=lambda x: x.order)]
            parent.reorder_todo_items(all_todo_ids)
