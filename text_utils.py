# coding:utf-8
"""
文本处理工具函数
用于处理长文本的显示和提示
"""

def truncate_text(text: str, max_length: int = 50) -> str:
    """
    截断文本，如果超过最大长度则添加省略号
    
    Args:
        text: 原始文本
        max_length: 最大显示长度
        
    Returns:
        截断后的文本
    """
    if not text:
        return ""
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length-3] + "..."

def should_show_tooltip(text: str, max_length: int = 50) -> bool:
    """
    判断是否需要显示工具提示
    
    Args:
        text: 原始文本
        max_length: 最大显示长度
        
    Returns:
        是否需要显示提示
    """
    return text and len(text) > max_length

def get_display_text_and_tooltip(text: str, max_length: int = 50) -> tuple[str, str]:
    """
    获取显示文本和工具提示文本
    
    Args:
        text: 原始文本
        max_length: 最大显示长度
        
    Returns:
        (显示文本, 工具提示文本)
    """
    if not text:
        return "", ""
    
    display_text = truncate_text(text, max_length)
    tooltip_text = text if should_show_tooltip(text, max_length) else ""
    
    return display_text, tooltip_text
