# coding:utf-8
"""
测试TODO分离功能
验证已完成和未完成TODO的分离显示逻辑
"""

from models import Project, TodoItem

def test_todo_separation():
    """测试TODO分离逻辑"""
    print("🎯 TODO分离功能测试")
    print("=" * 50)
    
    # 创建测试项目
    project = Project(name="测试项目", description="用于测试TODO分离功能")
    
    # 添加多个TODO项，包含已完成和未完成的
    todos_data = [
        ("完成用户界面设计", "设计登录和注册页面", False),
        ("实现用户认证", "添加JWT认证机制", True),
        ("数据库设计", "设计用户表和权限表", False),
        ("编写API文档", "使用Swagger编写接口文档", True),
        ("单元测试", "为核心功能编写测试用例", False),
        ("部署到生产环境", "配置服务器和域名", True),
        ("性能优化", "优化数据库查询和缓存", False),
        ("用户反馈收集", "添加反馈表单和统计", False),
    ]
    
    # 添加TODO项到项目
    for i, (title, desc, completed) in enumerate(todos_data):
        todo = project.add_todo(title, desc)
        todo.completed = completed
        todo.order = i
    
    print(f"✅ 创建了包含 {len(project.todos)} 个TODO的测试项目")
    print()
    
    # 分离TODO
    pending_todos = [todo for todo in project.todos if not todo.completed]
    completed_todos = [todo for todo in project.todos if todo.completed]
    
    # 按顺序排序
    pending_todos.sort(key=lambda x: x.order)
    completed_todos.sort(key=lambda x: x.order)
    
    print("📋 分离结果：")
    print(f"   未完成TODO: {len(pending_todos)} 个")
    print(f"   已完成TODO: {len(completed_todos)} 个")
    print()
    
    # 显示未完成列表
    print("📋 未完成TODO列表：")
    for i, todo in enumerate(pending_todos, 1):
        print(f"   {i}. {todo.title}")
        print(f"      描述: {todo.description}")
        print(f"      顺序: {todo.order}")
        print()
    
    # 显示已完成列表
    print("✅ 已完成TODO列表：")
    for i, todo in enumerate(completed_todos, 1):
        print(f"   {i}. {todo.title}")
        print(f"      描述: {todo.description}")
        print(f"      顺序: {todo.order}")
        print()
    
    # 测试状态切换
    print("🔄 测试状态切换：")
    if pending_todos:
        test_todo = pending_todos[0]
        print(f"将 '{test_todo.title}' 标记为已完成")
        test_todo.completed = True
        
        # 重新分离
        new_pending = [todo for todo in project.todos if not todo.completed]
        new_completed = [todo for todo in project.todos if todo.completed]
        
        print(f"   切换后未完成: {len(new_pending)} 个")
        print(f"   切换后已完成: {len(new_completed)} 个")
        print()
    
    # 测试拖拽排序逻辑
    print("🔀 测试拖拽排序逻辑：")
    if len(pending_todos) >= 2:
        print("原始未完成TODO顺序:")
        for i, todo in enumerate(pending_todos):
            print(f"   {i+1}. {todo.title} (order: {todo.order})")
        
        # 模拟拖拽：将第一个TODO移到最后
        todo_ids = [todo.id for todo in pending_todos]
        reordered_ids = todo_ids[1:] + [todo_ids[0]]  # 将第一个移到最后
        
        print("\n拖拽后的ID顺序:")
        for i, todo_id in enumerate(reordered_ids):
            todo = next(t for t in pending_todos if t.id == todo_id)
            print(f"   {i+1}. {todo.title}")
        
        # 更新order值
        todo_dict = {todo.id: todo for todo in pending_todos}
        for i, todo_id in enumerate(reordered_ids):
            if todo_id in todo_dict:
                todo_dict[todo_id].order = i
        
        print("\n更新order后:")
        sorted_todos = sorted(pending_todos, key=lambda x: x.order)
        for todo in sorted_todos:
            print(f"   {todo.title} (order: {todo.order})")
    
    print()
    print("🎨 界面布局说明：")
    print("   ┌─────────────────────────────────┐")
    print("   │ TODO列表                [添加]  │")
    print("   ├─────────────────────────────────┤")
    print("   │ 📋 未完成 (3)                   │")
    print("   │ ┌─────────────────────────────┐ │")
    print("   │ │ ○ 完成用户界面设计          │ │")
    print("   │ │ ○ 数据库设计                │ │")
    print("   │ │ ○ 单元测试                  │ │")
    print("   │ └─────────────────────────────┘ │")
    print("   │ ✅ 已完成 (3)                   │")
    print("   │ ┌─────────────────────────────┐ │")
    print("   │ │ ✓ 实现用户认证              │ │")
    print("   │ │ ✓ 编写API文档               │ │")
    print("   │ │ ✓ 部署到生产环境            │ │")
    print("   │ └─────────────────────────────┘ │")
    print("   └─────────────────────────────────┘")
    print()
    
    print("✨ 功能特点：")
    print("   • 自动分离：根据完成状态自动分组显示")
    print("   • 实时更新：状态改变时自动移动到对应列表")
    print("   • 独立排序：两个列表可以独立拖拽排序")
    print("   • 数量显示：标签显示每个列表的项目数量")
    print("   • 视觉区分：使用不同颜色和图标区分状态")
    print()
    
    return True

def test_edge_cases():
    """测试边界情况"""
    print("🧪 边界情况测试")
    print("=" * 30)
    
    # 测试空列表
    empty_project = Project(name="空项目", description="没有TODO的项目")
    pending = [todo for todo in empty_project.todos if not todo.completed]
    completed = [todo for todo in empty_project.todos if todo.completed]
    
    print("1. 空TODO列表:")
    print(f"   未完成: {len(pending)} 个")
    print(f"   已完成: {len(completed)} 个")
    print()
    
    # 测试只有未完成的TODO
    pending_only_project = Project(name="只有未完成", description="只有未完成TODO")
    for i in range(3):
        todo = pending_only_project.add_todo(f"未完成任务{i+1}", f"描述{i+1}")
        todo.completed = False
    
    pending = [todo for todo in pending_only_project.todos if not todo.completed]
    completed = [todo for todo in pending_only_project.todos if todo.completed]
    
    print("2. 只有未完成TODO:")
    print(f"   未完成: {len(pending)} 个")
    print(f"   已完成: {len(completed)} 个")
    print()
    
    # 测试只有已完成的TODO
    completed_only_project = Project(name="只有已完成", description="只有已完成TODO")
    for i in range(3):
        todo = completed_only_project.add_todo(f"已完成任务{i+1}", f"描述{i+1}")
        todo.completed = True
    
    pending = [todo for todo in completed_only_project.todos if not todo.completed]
    completed = [todo for todo in completed_only_project.todos if todo.completed]
    
    print("3. 只有已完成TODO:")
    print(f"   未完成: {len(pending)} 个")
    print(f"   已完成: {len(completed)} 个")
    print()
    
    print("✅ 边界情况测试通过")
    return True

if __name__ == "__main__":
    print("开始测试TODO分离功能...")
    print("=" * 60)
    
    try:
        # 测试基本分离功能
        success1 = test_todo_separation()
        
        # 测试边界情况
        success2 = test_edge_cases()
        
        print("=" * 60)
        if success1 and success2:
            print("所有TODO分离功能测试通过! ✅")
            print("\n现在可以运行应用程序查看实际效果：")
            print("python main.py")
        else:
            print("测试失败! ❌")
            
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        print("测试失败! ❌")
