# coding:utf-8
"""
TODO分离功能演示脚本
展示未完成和已完成TODO的分离显示功能
"""

from data_manager import DataManager

def demo_todo_separation_feature():
    """演示TODO分离功能"""
    print("🎯 TODO分离功能演示")
    print("=" * 60)
    
    print("📝 功能说明：")
    print("   TODO列表现在分为两个独立的部分：")
    print("   • 上方：📋 未完成 - 显示所有未完成的TODO项")
    print("   • 下方：✅ 已完成 - 显示所有已完成的TODO项")
    print("   • 状态改变时自动移动到对应列表")
    print("   • 两个列表可以独立拖拽排序")
    print()
    
    # 创建演示数据
    print("📊 创建演示数据...")
    dm = DataManager("demo_separation.json")
    
    # 清除现有项目
    for project in dm.get_projects():
        dm.remove_project(project.id)
    
    # 创建演示项目
    demo_project = dm.add_project(
        "TODO分离功能演示项目",
        "用于演示TODO分离显示功能的示例项目"
    )
    
    # 添加混合状态的TODO项
    todos_data = [
        ("设计用户界面", "创建登录和注册页面的UI设计", False),
        ("实现用户认证", "添加JWT认证和权限管理", True),
        ("数据库设计", "设计用户表、权限表等数据库结构", False),
        ("编写API文档", "使用Swagger编写完整的API文档", True),
        ("前端开发", "使用React开发用户界面", False),
        ("后端开发", "使用Python Flask开发后端API", False),
        ("单元测试", "为核心功能编写单元测试", True),
        ("集成测试", "编写端到端的集成测试", False),
        ("部署配置", "配置生产环境的部署脚本", True),
        ("性能优化", "优化数据库查询和API响应时间", False),
        ("安全审计", "进行安全漏洞检查和修复", False),
        ("用户培训", "编写用户手册和培训材料", True),
    ]
    
    # 添加TODO项
    for i, (title, desc, completed) in enumerate(todos_data):
        todo = demo_project.add_todo(title, desc)
        todo.completed = completed
        todo.order = i
    
    # 更新项目
    dm.update_project(demo_project)
    
    print(f"✅ 创建了包含 {len(demo_project.todos)} 个TODO的演示项目")
    print()
    
    # 分析TODO分布
    pending_todos = [todo for todo in demo_project.todos if not todo.completed]
    completed_todos = [todo for todo in demo_project.todos if todo.completed]
    
    print("📊 TODO分布统计：")
    print(f"   总计：{len(demo_project.todos)} 个TODO")
    print(f"   未完成：{len(pending_todos)} 个")
    print(f"   已完成：{len(completed_todos)} 个")
    print(f"   完成率：{len(completed_todos)/len(demo_project.todos)*100:.1f}%")
    print()
    
    # 显示分离效果
    print("📋 分离显示效果：")
    print()
    
    print("┌─────────────────────────────────────────────────────────┐")
    print("│ TODO列表                                        [添加]  │")
    print("├─────────────────────────────────────────────────────────┤")
    print(f"│ 📋 未完成 ({len(pending_todos)})                                           │")
    print("│ ┌─────────────────────────────────────────────────────┐ │")
    
    for i, todo in enumerate(pending_todos[:5], 1):  # 只显示前5个
        title = todo.title[:35] + "..." if len(todo.title) > 35 else todo.title
        print(f"│ │ ○ {title:<40} │ │")
    
    if len(pending_todos) > 5:
        print(f"│ │ ... 还有 {len(pending_todos)-5} 个未完成项目                    │ │")
    
    print("│ └─────────────────────────────────────────────────────┘ │")
    print(f"│ ✅ 已完成 ({len(completed_todos)})                                           │")
    print("│ ┌─────────────────────────────────────────────────────┐ │")
    
    for i, todo in enumerate(completed_todos[:5], 1):  # 只显示前5个
        title = todo.title[:35] + "..." if len(todo.title) > 35 else todo.title
        print(f"│ │ ✓ {title:<40} │ │")
    
    if len(completed_todos) > 5:
        print(f"│ │ ... 还有 {len(completed_todos)-5} 个已完成项目                    │ │")
    
    print("│ └─────────────────────────────────────────────────────┘ │")
    print("└─────────────────────────────────────────────────────────┘")
    print()
    
    # 功能特点说明
    print("✨ 功能特点：")
    print("   1. 🎯 智能分离：根据完成状态自动分组显示")
    print("   2. 🔄 实时更新：勾选/取消勾选时自动移动到对应列表")
    print("   3. 🖱️ 独立排序：两个列表可以分别进行拖拽排序")
    print("   4. 📊 数量统计：标签实时显示每个列表的项目数量")
    print("   5. 🎨 视觉区分：使用不同颜色和图标区分状态")
    print("   6. 📱 响应式设计：两个列表平分空间，可独立滚动")
    print()
    
    # 操作演示
    print("🎬 操作演示：")
    print("   1. 启动应用：python main.py")
    print("   2. 点击进入演示项目")
    print("   3. 观察TODO列表的分离显示效果")
    print("   4. 勾选未完成项目，观察其移动到已完成列表")
    print("   5. 取消勾选已完成项目，观察其移动到未完成列表")
    print("   6. 在各自列表中拖拽项目进行排序")
    print("   7. 观察标签中的数量统计变化")
    print()
    
    # 使用场景
    print("🎯 使用场景：")
    print("   • 项目管理：清晰区分已完成和待办任务")
    print("   • 进度跟踪：直观查看项目完成情况")
    print("   • 任务规划：专注于未完成任务的安排")
    print("   • 成果展示：突出显示已完成的工作")
    print("   • 团队协作：清晰的状态可视化")
    print()
    
    # 技术优势
    print("🔧 技术优势：")
    print("   • 自动化：状态改变时无需手动移动")
    print("   • 高效性：独立列表提升操作效率")
    print("   • 一致性：保持数据模型的完整性")
    print("   • 可扩展：为未来功能扩展奠定基础")
    print()
    
    # 清理演示数据
    print("🧹 清理演示数据...")
    dm.remove_project(demo_project.id)
    print("✅ 演示数据已清理")
    print()
    
    print("🎉 TODO分离功能演示完成！")
    print("现在可以运行应用程序体验实际效果：")
    print("python main.py")

def create_mixed_status_data():
    """创建包含混合状态的测试数据"""
    print("\n📝 创建混合状态测试数据...")
    
    dm = DataManager()
    
    # 添加一个包含混合状态TODO的项目
    mixed_project = dm.add_project(
        "混合状态测试项目",
        "包含各种完成状态的TODO项目，用于测试分离显示功能"
    )
    
    # 添加各种状态的TODO
    mixed_todos = [
        ("需求分析", "收集和分析用户需求", False),
        ("技术选型", "选择合适的技术栈", True),
        ("架构设计", "设计系统整体架构", False),
        ("数据库设计", "设计数据库表结构", True),
        ("前端开发", "开发用户界面", False),
        ("后端开发", "开发服务端API", False),
        ("接口联调", "前后端接口对接", True),
        ("功能测试", "测试各项功能", False),
        ("性能测试", "进行性能压力测试", False),
        ("安全测试", "进行安全漏洞测试", True),
        ("用户验收", "用户验收测试", False),
        ("部署上线", "部署到生产环境", True),
    ]
    
    for i, (title, desc, completed) in enumerate(mixed_todos):
        todo = mixed_project.add_todo(title, desc)
        todo.completed = completed
        todo.order = i
    
    dm.update_project(mixed_project)
    
    pending_count = len([t for t in mixed_project.todos if not t.completed])
    completed_count = len([t for t in mixed_project.todos if t.completed])
    
    print(f"✅ 创建了混合状态测试项目")
    print(f"   未完成：{pending_count} 个")
    print(f"   已完成：{completed_count} 个")
    print(f"   总计：{len(mixed_project.todos)} 个")

if __name__ == "__main__":
    print("开始TODO分离功能演示...")
    print("=" * 70)
    
    try:
        # 演示主要功能
        demo_todo_separation_feature()
        
        # 创建测试数据
        create_mixed_status_data()
        
        print("=" * 70)
        print("演示完成! ✅")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        print("演示失败! ❌")
