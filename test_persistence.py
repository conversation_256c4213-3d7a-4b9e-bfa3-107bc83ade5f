# coding:utf-8
"""
数据持久化测试脚本
验证数据保存和加载功能
"""

import os
import json
from data_manager import DataManager
from models import Project

def test_data_persistence():
    """测试数据持久化功能"""
    print("=== 测试数据持久化功能 ===")
    
    # 使用测试数据文件
    test_file = "test_persistence.json"
    
    # 清理之前的测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
        print("清理之前的测试文件")
    
    # 第一步：创建数据管理器并添加数据
    print("\n1. 创建新的数据管理器...")
    dm1 = DataManager(test_file)
    
    # 添加测试项目
    project1 = dm1.add_project("测试项目1", "这是第一个测试项目")
    project2 = dm1.add_project("测试项目2", "这是第二个测试项目")
    
    # 为项目添加TODO和Issue
    project1.add_todo("完成功能A", "实现基础功能")
    project1.add_todo("完成功能B", "实现高级功能")
    project1.add_issue("修复Bug", "修复登录问题")
    
    project2.add_todo("设计界面", "创建用户界面")
    project2.add_issue("性能优化", "提升响应速度")
    
    # 更新项目以触发保存
    dm1.update_project(project1)
    dm1.update_project(project2)
    
    print(f"添加了 {len(dm1.get_projects())} 个项目")
    print(f"项目1有 {len(project1.todos)} 个TODO和 {len(project1.issues)} 个Issue")
    print(f"项目2有 {len(project2.todos)} 个TODO和 {len(project2.issues)} 个Issue")
    
    # 第二步：验证文件是否创建并包含正确数据
    print("\n2. 验证数据文件...")
    if os.path.exists(test_file):
        print("✅ 数据文件已创建")
        
        # 读取文件内容
        with open(test_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        projects_data = data.get('projects', [])
        print(f"✅ 文件包含 {len(projects_data)} 个项目")
        
        # 验证项目数据
        for i, project_data in enumerate(projects_data):
            print(f"   项目{i+1}: {project_data['name']}")
            print(f"   - TODO数量: {len(project_data.get('todos', []))}")
            print(f"   - Issue数量: {len(project_data.get('issues', []))}")
    else:
        print("❌ 数据文件未创建")
        return False
    
    # 第三步：创建新的数据管理器实例，验证数据加载
    print("\n3. 测试数据加载...")
    dm2 = DataManager(test_file)
    loaded_projects = dm2.get_projects()
    
    print(f"✅ 加载了 {len(loaded_projects)} 个项目")
    
    # 验证加载的数据
    for i, project in enumerate(loaded_projects):
        print(f"   项目{i+1}: {project.name}")
        print(f"   - 描述: {project.description}")
        print(f"   - TODO数量: {len(project.todos)}")
        print(f"   - Issue数量: {len(project.issues)}")
        
        # 验证TODO数据
        for j, todo in enumerate(project.todos):
            print(f"     TODO{j+1}: {todo.title} (完成: {todo.completed})")
        
        # 验证Issue数据
        for j, issue in enumerate(project.issues):
            print(f"     Issue{j+1}: {issue.title}")
    
    # 第四步：测试数据修改和保存
    print("\n4. 测试数据修改...")
    if loaded_projects:
        test_project = loaded_projects[0]
        
        # 添加新的TODO
        new_todo = test_project.add_todo("新增TODO", "这是新增的TODO")
        print(f"✅ 添加新TODO: {new_todo.title}")
        
        # 标记第一个TODO为完成
        if test_project.todos:
            test_project.todos[0].completed = True
            print(f"✅ 标记TODO为完成: {test_project.todos[0].title}")
        
        # 保存修改
        dm2.update_project(test_project)
        print("✅ 保存修改")
    
    # 第五步：再次验证数据持久化
    print("\n5. 验证修改后的数据...")
    dm3 = DataManager(test_file)
    final_projects = dm3.get_projects()
    
    if final_projects:
        test_project = final_projects[0]
        print(f"✅ 项目现在有 {len(test_project.todos)} 个TODO")
        
        completed_todos = [todo for todo in test_project.todos if todo.completed]
        print(f"✅ 其中 {len(completed_todos)} 个已完成")
        
        # 显示所有TODO状态
        for todo in test_project.todos:
            status = "✓" if todo.completed else "○"
            print(f"   {status} {todo.title}")
    
    # 清理测试文件
    print("\n6. 清理测试文件...")
    if os.path.exists(test_file):
        os.remove(test_file)
        print("✅ 测试文件已清理")
    
    print("\n=== 数据持久化测试完成 ===")
    return True

def test_real_data_file():
    """测试实际应用的数据文件"""
    print("\n=== 检查实际应用数据文件 ===")
    
    real_file = "todo_data.json"
    if os.path.exists(real_file):
        print(f"✅ 找到实际数据文件: {real_file}")
        
        dm = DataManager(real_file)
        projects = dm.get_projects()
        
        print(f"✅ 当前有 {len(projects)} 个项目")
        
        total_todos = sum(len(p.todos) for p in projects)
        total_issues = sum(len(p.issues) for p in projects)
        completed_todos = sum(len([t for t in p.todos if t.completed]) for p in projects)
        
        print(f"✅ 总计: {total_todos} 个TODO ({completed_todos} 个已完成), {total_issues} 个Issue")
        
        for project in projects:
            print(f"   📁 {project.name}: {len(project.todos)} TODO, {len(project.issues)} Issue")
    else:
        print(f"❌ 未找到实际数据文件: {real_file}")
        print("   请先运行应用程序以创建数据文件")

if __name__ == "__main__":
    print("开始数据持久化测试...")
    print("=" * 50)
    
    try:
        # 测试数据持久化功能
        success = test_data_persistence()
        
        # 检查实际数据文件
        test_real_data_file()
        
        print("=" * 50)
        if success:
            print("所有测试通过! ✅")
        else:
            print("测试失败! ❌")
            
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        print("测试失败! ❌")
