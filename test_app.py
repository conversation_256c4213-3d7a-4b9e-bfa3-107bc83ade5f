# coding:utf-8
"""
TODO应用程序测试脚本
用于验证数据模型和基本功能
"""

from models import Project, TodoItem, IssueItem
from data_manager import DataManager

def test_models():
    """测试数据模型"""
    print("=== 测试数据模型 ===")
    
    # 创建项目
    project = Project(name="测试项目", description="这是一个测试项目")
    print(f"创建项目: {project.name}")
    
    # 添加TODO项
    todo1 = project.add_todo("完成功能A", "实现基础功能")
    todo2 = project.add_todo("完成功能B", "实现高级功能")
    print(f"添加TODO项: {len(project.todos)}个")
    
    # 添加Issue项
    issue1 = project.add_issue("Bug修复", "修复登录问题")
    issue2 = project.add_issue("性能优化", "提升响应速度")
    print(f"添加Issue项: {len(project.issues)}个")
    
    # 测试TODO完成状态
    todo1.completed = True
    print(f"TODO1完成状态: {todo1.completed}")
    
    # 测试重新排序
    original_order = [todo.id for todo in project.todos]
    print(f"原始TODO顺序: {[todo.title for todo in project.todos]}")
    
    # 重新排序
    project.reorder_todos([todo2.id, todo1.id])
    print(f"重新排序后: {[todo.title for todo in project.todos]}")
    
    print("数据模型测试完成!\n")

def test_data_manager():
    """测试数据管理器"""
    print("=== 测试数据管理器 ===")
    
    # 创建数据管理器
    dm = DataManager("test_data.json")
    
    # 获取项目列表
    projects = dm.get_projects()
    print(f"当前项目数量: {len(projects)}")
    
    # 添加新项目
    new_project = dm.add_project("新测试项目", "这是通过数据管理器创建的项目")
    print(f"添加新项目: {new_project.name}")
    
    # 更新项目
    new_project.add_todo("测试任务", "这是一个测试任务")
    dm.update_project(new_project)
    print("项目更新完成")
    
    # 重新加载验证
    dm2 = DataManager("test_data.json")
    projects2 = dm2.get_projects()
    print(f"重新加载后项目数量: {len(projects2)}")
    
    # 查找刚才添加的项目
    found_project = dm2.get_project(new_project.id)
    if found_project:
        print(f"找到项目: {found_project.name}, TODO数量: {len(found_project.todos)}")
    
    print("数据管理器测试完成!\n")

def test_project_operations():
    """测试项目操作"""
    print("=== 测试项目操作 ===")
    
    project = Project(name="操作测试项目", description="测试各种操作")
    
    # 添加多个TODO项
    todos = []
    for i in range(5):
        todo = project.add_todo(f"任务{i+1}", f"这是第{i+1}个任务")
        todos.append(todo)
    
    print(f"添加了{len(todos)}个TODO项")
    
    # 删除中间的TODO项
    removed = project.remove_todo(todos[2].id)
    print(f"删除TODO项结果: {removed}")
    print(f"删除后TODO数量: {len(project.todos)}")
    
    # 验证顺序重新编号
    for i, todo in enumerate(project.todos):
        print(f"TODO {i}: {todo.title}, order: {todo.order}")
    
    # 测试Issue操作
    issues = []
    for i in range(3):
        issue = project.add_issue(f"问题{i+1}", f"这是第{i+1}个问题")
        issues.append(issue)
    
    print(f"添加了{len(issues)}个Issue项")
    
    # 重新排序Issue
    issue_ids = [issue.id for issue in reversed(issues)]
    project.reorder_issues(issue_ids)
    
    print("重新排序后的Issue:")
    for i, issue in enumerate(project.issues):
        print(f"Issue {i}: {issue.title}, order: {issue.order}")
    
    print("项目操作测试完成!\n")

if __name__ == "__main__":
    print("开始测试TODO应用程序...")
    print("=" * 50)
    
    try:
        test_models()
        test_data_manager()
        test_project_operations()
        
        print("=" * 50)
        print("所有测试完成! ✅")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        print("测试失败! ❌")
