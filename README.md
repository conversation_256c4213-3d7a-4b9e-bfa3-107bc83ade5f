# TODO项目管理器

一个基于PyQt6和qfluentwidgets的现代化TODO应用程序，用于管理工作项目中的任务和问题。

## 功能特性

### 项目管理
- 创建、查看项目列表
- 每个项目显示名称、描述和统计信息
- 点击项目卡片进入详情页面

### TODO管理
- 添加、编辑、删除TODO项
- 标记TODO项为完成/未完成状态
- 拖拽重新排序TODO项
- 每个TODO项包含标题和描述

### Issue管理
- 添加、编辑、删除Issue项
- 拖拽重新排序Issue项
- 每个Issue项包含标题和描述

### 数据持久化
- 自动保存数据到本地JSON文件
- 应用重启后数据保持不变

## 安装要求

```bash
pip install PyQt6
pip install qfluentwidgets
```

## 运行应用

```bash
python main.py
```

## 项目结构

```
project_todo/
├── main.py                    # 应用程序入口
├── todo_app.py               # 主应用窗口
├── models.py                 # 数据模型定义
├── data_manager.py           # 数据持久化管理
├── project_list_widget.py    # 项目列表页面
├── project_detail_widget.py  # 项目详情页面
├── todo_panel.py             # TODO面板组件
├── issue_panel.py            # Issue面板组件
├── todo_data.json            # 数据存储文件（自动生成）
└── README.md                 # 说明文档
```

## 使用说明

### 1. 项目列表页面
- 启动应用后首先看到项目列表
- 点击"添加项目"按钮创建新项目
- 点击项目卡片进入项目详情

### 2. 项目详情页面
- 左侧为TODO面板，右侧为Issue面板
- 点击"返回"按钮回到项目列表

### 3. TODO操作
- 点击"添加TODO"创建新任务
- 点击复选框标记完成状态
- 点击编辑按钮修改TODO内容
- 点击删除按钮移除TODO
- 拖拽TODO项可以重新排序

### 4. Issue操作
- 点击"添加Issue"创建新问题
- 点击编辑按钮修改Issue内容
- 点击删除按钮移除Issue
- 拖拽Issue项可以重新排序

## 示例数据

应用首次运行时会自动创建示例项目和数据，包括：
- 网站开发项目（包含3个TODO和2个Issue）
- 移动应用开发项目（包含2个TODO和1个Issue）

## 技术栈

- **PyQt6**: 现代化的Python GUI框架
- **qfluentwidgets**: 基于Fluent Design的UI组件库
- **Python dataclasses**: 数据模型定义
- **JSON**: 数据持久化存储

## 特色功能

1. **现代化UI设计**: 采用Fluent Design风格，界面美观易用
2. **拖拽排序**: 支持拖拽重新排序TODO和Issue项
3. **实时保存**: 所有操作自动保存到本地文件
4. **响应式布局**: 支持窗口大小调整和面板分割
5. **完整的CRUD操作**: 支持创建、读取、更新、删除所有数据项

## 快速开始

1. **安装依赖**：
   ```bash
   pip install -r requirements.txt
   ```

2. **运行应用**：
   ```bash
   python main.py
   ```
   或者双击 `run.bat` 文件（Windows）

3. **运行测试**：
   ```bash
   python test_app.py
   ```

## 功能演示

### 主要功能已实现：
✅ 项目列表显示和管理
✅ 项目详情页面（TODO + Issue双面板）
✅ TODO项的增删改查和完成状态切换
✅ Issue项的增删改查
✅ 拖拽重新排序（TODO和Issue）
✅ 数据自动保存和加载
✅ 现代化Fluent Design UI

### 界面特色：
- 🎨 美观的Fluent Design风格界面
- 📱 响应式布局，支持窗口大小调整
- 🖱️ 直观的拖拽排序操作
- ⚡ 实时数据保存，无需手动保存
- 🔄 流畅的页面切换动画

## 扩展建议

- 添加项目搜索和过滤功能
- 支持TODO项设置截止日期
- 添加Issue优先级和状态管理
- 支持数据导出和导入
- 添加主题切换功能
- 支持多用户和云同步

## 开发说明

本项目采用模块化设计，各组件职责清晰：
- `models.py`: 数据模型层
- `data_manager.py`: 数据持久化层
- `*_widget.py`: UI组件层
- `todo_app.py`: 应用主控制器

代码结构清晰，易于扩展和维护。
