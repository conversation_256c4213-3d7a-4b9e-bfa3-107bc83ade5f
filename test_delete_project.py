# coding:utf-8
"""
测试项目删除功能
"""

import os
from data_manager import DataManager
from models import Project

def test_project_deletion():
    """测试项目删除功能"""
    print("=== 测试项目删除功能 ===")
    
    # 使用测试数据文件
    test_file = "test_delete.json"
    
    # 清理之前的测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
        print("清理之前的测试文件")
    
    # 创建数据管理器并添加测试数据
    print("\n1. 创建测试项目...")
    dm = DataManager(test_file)
    
    # 添加多个测试项目
    project1 = dm.add_project("测试项目1", "第一个测试项目")
    project2 = dm.add_project("测试项目2", "第二个测试项目")
    project3 = dm.add_project("测试项目3", "第三个测试项目")
    
    # 为项目添加一些数据
    project1.add_todo("任务1", "第一个任务")
    project1.add_todo("任务2", "第二个任务")
    project1.add_issue("问题1", "第一个问题")
    
    project2.add_todo("任务A", "项目2的任务")
    project2.add_issue("问题A", "项目2的问题")
    project2.add_issue("问题B", "项目2的另一个问题")
    
    # 更新项目以保存数据
    dm.update_project(project1)
    dm.update_project(project2)
    
    print(f"✅ 创建了 {len(dm.get_projects())} 个项目")
    
    # 显示项目详情
    for i, project in enumerate(dm.get_projects()):
        print(f"   项目{i+1}: {project.name}")
        print(f"   - TODO数量: {len(project.todos)}")
        print(f"   - Issue数量: {len(project.issues)}")
    
    # 测试删除中间的项目
    print(f"\n2. 删除项目: {project2.name}")
    project2_id = project2.id
    success = dm.remove_project(project2_id)
    
    if success:
        print("✅ 项目删除成功")
    else:
        print("❌ 项目删除失败")
        return False
    
    # 验证删除结果
    remaining_projects = dm.get_projects()
    print(f"✅ 剩余项目数量: {len(remaining_projects)}")
    
    # 验证被删除的项目不再存在
    deleted_project = dm.get_project(project2_id)
    if deleted_project is None:
        print("✅ 被删除的项目确实不存在了")
    else:
        print("❌ 被删除的项目仍然存在")
        return False
    
    # 显示剩余项目
    print("\n剩余项目:")
    for i, project in enumerate(remaining_projects):
        print(f"   项目{i+1}: {project.name}")
        print(f"   - TODO数量: {len(project.todos)}")
        print(f"   - Issue数量: {len(project.issues)}")
    
    # 测试删除不存在的项目
    print("\n3. 测试删除不存在的项目...")
    fake_id = "fake-project-id"
    success = dm.remove_project(fake_id)
    
    if not success:
        print("✅ 删除不存在的项目正确返回False")
    else:
        print("❌ 删除不存在的项目错误返回True")
        return False
    
    # 验证数据持久化
    print("\n4. 验证数据持久化...")
    dm2 = DataManager(test_file)
    loaded_projects = dm2.get_projects()
    
    if len(loaded_projects) == len(remaining_projects):
        print("✅ 数据持久化正确")
    else:
        print("❌ 数据持久化失败")
        return False
    
    # 验证项目内容
    for original, loaded in zip(remaining_projects, loaded_projects):
        if (original.name == loaded.name and 
            len(original.todos) == len(loaded.todos) and 
            len(original.issues) == len(loaded.issues)):
            print(f"✅ 项目 '{original.name}' 数据完整")
        else:
            print(f"❌ 项目 '{original.name}' 数据不完整")
            return False
    
    # 测试删除所有项目
    print("\n5. 测试删除所有项目...")
    for project in loaded_projects:
        dm2.remove_project(project.id)
    
    final_projects = dm2.get_projects()
    if len(final_projects) == 0:
        print("✅ 所有项目删除成功")
    else:
        print(f"❌ 仍有 {len(final_projects)} 个项目未删除")
        return False
    
    # 清理测试文件
    print("\n6. 清理测试文件...")
    if os.path.exists(test_file):
        os.remove(test_file)
        print("✅ 测试文件已清理")
    
    print("\n=== 项目删除功能测试完成 ===")
    return True

def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    test_file = "test_edge_cases.json"
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
    
    # 测试空项目列表删除
    print("\n1. 测试空项目列表删除...")
    dm = DataManager(test_file)
    
    # 确保项目列表为空
    projects = dm.get_projects()
    for project in projects:
        dm.remove_project(project.id)
    
    # 尝试删除不存在的项目
    success = dm.remove_project("non-existent-id")
    if not success:
        print("✅ 空列表删除测试通过")
    else:
        print("❌ 空列表删除测试失败")
        return False
    
    # 测试删除包含大量数据的项目
    print("\n2. 测试删除包含大量数据的项目...")
    big_project = dm.add_project("大型项目", "包含大量数据的项目")
    
    # 添加大量TODO和Issue
    for i in range(50):
        big_project.add_todo(f"TODO {i+1}", f"这是第{i+1}个TODO")
    
    for i in range(30):
        big_project.add_issue(f"Issue {i+1}", f"这是第{i+1}个Issue")
    
    dm.update_project(big_project)
    
    print(f"   创建了包含 {len(big_project.todos)} 个TODO和 {len(big_project.issues)} 个Issue的项目")
    
    # 删除大型项目
    success = dm.remove_project(big_project.id)
    if success:
        print("✅ 大型项目删除成功")
    else:
        print("❌ 大型项目删除失败")
        return False
    
    # 验证删除
    if dm.get_project(big_project.id) is None:
        print("✅ 大型项目确实被删除")
    else:
        print("❌ 大型项目删除验证失败")
        return False
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
        print("✅ 边界测试文件已清理")
    
    print("\n=== 边界情况测试完成 ===")
    return True

if __name__ == "__main__":
    print("开始测试项目删除功能...")
    print("=" * 50)
    
    try:
        # 测试基本删除功能
        success1 = test_project_deletion()
        
        # 测试边界情况
        success2 = test_edge_cases()
        
        print("=" * 50)
        if success1 and success2:
            print("所有删除功能测试通过! ✅")
        else:
            print("删除功能测试失败! ❌")
            
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        print("测试失败! ❌")
