# coding:utf-8
import json
import os
from typing import List, Optional
from models import Project, TodoItem, IssueItem
from datetime import datetime

class DataManager:
    """数据持久化管理器"""
    
    def __init__(self, data_file: str = "todo_data.json"):
        self.data_file = data_file
        self.projects: List[Project] = []
        self.load_data()
    
    def load_data(self):
        """从文件加载数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.projects = self._deserialize_projects(data.get('projects', []))
            except Exception as e:
                print(f"加载数据失败: {e}")
                self.projects = []
        else:
            # 创建示例数据
            self._create_sample_data()
    
    def save_data(self):
        """保存数据到文件"""
        try:
            data = {
                'projects': self._serialize_projects(self.projects)
            }
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存数据失败: {e}")
    
    def _serialize_projects(self, projects: List[Project]) -> List[dict]:
        """序列化项目数据"""
        result = []
        for project in projects:
            project_data = {
                'id': project.id,
                'name': project.name,
                'description': project.description,
                'created_at': project.created_at.isoformat(),
                'todos': [
                    {
                        'id': todo.id,
                        'title': todo.title,
                        'description': todo.description,
                        'completed': todo.completed,
                        'created_at': todo.created_at.isoformat(),
                        'order': todo.order
                    }
                    for todo in project.todos
                ],
                'issues': [
                    {
                        'id': issue.id,
                        'title': issue.title,
                        'description': issue.description,
                        'created_at': issue.created_at.isoformat(),
                        'order': issue.order
                    }
                    for issue in project.issues
                ]
            }
            result.append(project_data)
        return result
    
    def _deserialize_projects(self, projects_data: List[dict]) -> List[Project]:
        """反序列化项目数据"""
        result = []
        for project_data in projects_data:
            project = Project(
                id=project_data['id'],
                name=project_data['name'],
                description=project_data['description'],
                created_at=datetime.fromisoformat(project_data['created_at'])
            )
            
            # 反序列化TODO项
            for todo_data in project_data.get('todos', []):
                todo = TodoItem(
                    id=todo_data['id'],
                    title=todo_data['title'],
                    description=todo_data['description'],
                    completed=todo_data['completed'],
                    created_at=datetime.fromisoformat(todo_data['created_at']),
                    order=todo_data['order']
                )
                project.todos.append(todo)
            
            # 反序列化Issue项
            for issue_data in project_data.get('issues', []):
                issue = IssueItem(
                    id=issue_data['id'],
                    title=issue_data['title'],
                    description=issue_data['description'],
                    created_at=datetime.fromisoformat(issue_data['created_at']),
                    order=issue_data['order']
                )
                project.issues.append(issue)
            
            result.append(project)
        return result
    
    def _create_sample_data(self):
        """创建示例数据"""
        project1 = Project(name="网站开发项目", description="开发公司官方网站")
        project1.add_todo("设计首页布局", "创建响应式首页设计")
        project1.add_todo("实现用户登录", "添加用户认证功能")
        project1.add_todo("优化页面性能", "提升页面加载速度")
        project1.add_issue("登录按钮样式问题", "登录按钮在移动端显示异常")
        project1.add_issue("数据库连接超时", "在高并发情况下数据库连接超时")
        
        project2 = Project(name="移动应用开发", description="开发iOS和Android应用")
        project2.add_todo("完成UI设计", "设计应用界面和交互")
        project2.add_todo("实现核心功能", "开发主要业务逻辑")
        project2.add_issue("iOS适配问题", "应用在某些iOS版本上崩溃")
        
        self.projects = [project1, project2]
        self.save_data()
    
    def get_projects(self) -> List[Project]:
        """获取所有项目"""
        return self.projects
    
    def get_project(self, project_id: str) -> Optional[Project]:
        """根据ID获取项目"""
        for project in self.projects:
            if project.id == project_id:
                return project
        return None
    
    def add_project(self, name: str, description: str = "") -> Project:
        """添加新项目"""
        project = Project(name=name, description=description)
        self.projects.append(project)
        self.save_data()
        return project
    
    def remove_project(self, project_id: str) -> bool:
        """删除项目"""
        for i, project in enumerate(self.projects):
            if project.id == project_id:
                self.projects.pop(i)
                self.save_data()
                return True
        return False
    
    def update_project(self, project: Project):
        """更新项目"""
        for i, p in enumerate(self.projects):
            if p.id == project.id:
                self.projects[i] = project
                self.save_data()
                return True
        return False
