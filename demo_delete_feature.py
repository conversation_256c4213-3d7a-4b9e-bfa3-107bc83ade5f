# coding:utf-8
"""
项目删除功能演示脚本
展示新增的项目删除功能
"""

import os
from data_manager import DataManager

def demo_delete_feature():
    """演示项目删除功能"""
    print("🎯 项目删除功能演示")
    print("=" * 50)
    
    # 使用演示数据文件
    demo_file = "demo_delete.json"
    
    # 清理之前的演示文件
    if os.path.exists(demo_file):
        os.remove(demo_file)
    
    # 创建演示数据
    print("📝 创建演示项目...")
    dm = DataManager(demo_file)
    
    # 添加演示项目
    project1 = dm.add_project("网站重构项目", "重构公司官网，提升用户体验")
    project2 = dm.add_project("移动端开发", "开发iOS和Android应用")
    project3 = dm.add_project("数据分析平台", "构建数据可视化平台")
    project4 = dm.add_project("测试项目", "这是一个测试项目，可以安全删除")
    
    # 为项目添加内容
    project1.add_todo("设计新界面", "创建现代化的用户界面")
    project1.add_todo("优化性能", "提升页面加载速度")
    project1.add_issue("兼容性问题", "在IE浏览器中显示异常")
    
    project2.add_todo("完成原型设计", "设计应用原型")
    project2.add_todo("开发核心功能", "实现主要业务逻辑")
    project2.add_issue("内存泄漏", "应用长时间运行后内存占用过高")
    project2.add_issue("崩溃问题", "在某些设备上应用会崩溃")
    
    project3.add_todo("数据收集", "收集和整理数据源")
    project3.add_todo("图表开发", "开发各种数据图表")
    project3.add_todo("报告生成", "自动生成数据报告")
    project3.add_issue("性能优化", "大数据量时查询速度慢")
    
    project4.add_todo("测试任务1", "这是一个测试任务")
    project4.add_todo("测试任务2", "这是另一个测试任务")
    project4.add_issue("测试问题", "这是一个测试问题")
    
    # 更新项目
    dm.update_project(project1)
    dm.update_project(project2)
    dm.update_project(project3)
    dm.update_project(project4)
    
    print(f"✅ 创建了 {len(dm.get_projects())} 个演示项目")
    print()
    
    # 显示所有项目
    print("📋 当前项目列表：")
    projects = dm.get_projects()
    for i, project in enumerate(projects, 1):
        todo_count = len(project.todos)
        issue_count = len(project.issues)
        completed_count = sum(1 for todo in project.todos if todo.completed)
        
        print(f"   {i}. {project.name}")
        print(f"      描述: {project.description}")
        print(f"      TODO: {completed_count}/{todo_count} | Issues: {issue_count}")
        print()
    
    # 演示删除功能
    print("🗑️  删除功能演示：")
    print()
    
    # 删除测试项目
    print(f"正在删除项目: {project4.name}")
    print("💡 在实际应用中，会弹出确认对话框：")
    print(f"   标题: 确认删除项目")
    print(f"   内容: 确定要删除项目 '{project4.name}' 吗？")
    print(f"   说明: 此操作将删除项目中的所有TODO和Issue，且无法撤销。")
    print()
    
    # 执行删除
    success = dm.remove_project(project4.id)
    
    if success:
        print("✅ 项目删除成功！")
    else:
        print("❌ 项目删除失败！")
    
    print()
    
    # 显示删除后的项目列表
    print("📋 删除后的项目列表：")
    remaining_projects = dm.get_projects()
    for i, project in enumerate(remaining_projects, 1):
        todo_count = len(project.todos)
        issue_count = len(project.issues)
        completed_count = sum(1 for todo in project.todos if todo.completed)
        
        print(f"   {i}. {project.name}")
        print(f"      描述: {project.description}")
        print(f"      TODO: {completed_count}/{todo_count} | Issues: {issue_count}")
        print()
    
    print(f"📊 统计信息：")
    print(f"   删除前项目数量: {len(projects)}")
    print(f"   删除后项目数量: {len(remaining_projects)}")
    print(f"   成功删除: {len(projects) - len(remaining_projects)} 个项目")
    print()
    
    # 验证被删除的项目不存在
    deleted_project = dm.get_project(project4.id)
    if deleted_project is None:
        print("✅ 验证通过：被删除的项目确实不存在")
    else:
        print("❌ 验证失败：被删除的项目仍然存在")
    
    print()
    
    # 演示数据持久化
    print("💾 数据持久化验证：")
    dm2 = DataManager(demo_file)
    loaded_projects = dm2.get_projects()
    
    if len(loaded_projects) == len(remaining_projects):
        print("✅ 数据持久化正常：删除操作已保存到文件")
    else:
        print("❌ 数据持久化异常：删除操作未正确保存")
    
    print()
    
    # 功能特点说明
    print("🌟 项目删除功能特点：")
    print("   1. 🛡️  安全删除：删除前会显示确认对话框")
    print("   2. 📝 详细提示：显示项目名称和警告信息")
    print("   3. 🔄 智能返回：删除当前查看的项目时自动返回列表")
    print("   4. 💾 数据同步：删除操作立即保存到文件")
    print("   5. 🎨 界面友好：删除按钮位于项目卡片右上角")
    print("   6. 🚫 防误操作：点击删除按钮不会触发项目进入")
    print()
    
    # 使用说明
    print("📖 使用说明：")
    print("   1. 在项目列表页面，每个项目卡片右上角有删除按钮")
    print("   2. 点击删除按钮会弹出确认对话框")
    print("   3. 确认删除后，项目及其所有数据将被永久删除")
    print("   4. 如果正在查看被删除的项目，会自动返回项目列表")
    print("   5. 删除操作会立即保存，无需手动保存")
    print()
    
    # 清理演示文件
    print("🧹 清理演示文件...")
    if os.path.exists(demo_file):
        os.remove(demo_file)
        print("✅ 演示文件已清理")
    
    print()
    print("🎉 项目删除功能演示完成！")
    print("=" * 50)

if __name__ == "__main__":
    demo_delete_feature()
