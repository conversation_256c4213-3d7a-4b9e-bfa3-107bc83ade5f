# coding:utf-8
from PyQt6.QtWidgets import <PERSON>Widget, QVBoxLayout, QHBoxLayout, QLabel, QScrollArea
from PyQt6.QtCore import Qt, pyqtSignal
from qfluentwidgets import (PrimaryPushButton, PushButton, CardWidget, BodyLabel,
                           TitleLabel, SubtitleLabel, FluentIcon as FIF,
                           MessageBox, LineEdit, TextEdit, ToolButton)
from models import Project
from typing import List

class ProjectCard(CardWidget):
    """项目卡片组件"""
    clicked = pyqtSignal(str)  # 发送项目ID
    delete_requested = pyqtSignal(str)  # 发送要删除的项目ID

    def __init__(self, project: Project, parent=None):
        super().__init__(parent)
        self.project = project
        self.setFixedHeight(120)
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(16, 12, 16, 12)

        # 左侧内容区域
        content_layout = QVBoxLayout()

        # 项目名称
        self.name_label = SubtitleLabel(self.project.name)
        self.name_label.setStyleSheet("font-weight: bold;")

        # 项目描述
        self.desc_label = BodyLabel(self.project.description)
        self.desc_label.setWordWrap(True)

        # 统计信息
        todo_count = len(self.project.todos)
        issue_count = len(self.project.issues)
        completed_count = sum(1 for todo in self.project.todos if todo.completed)

        stats_text = f"TODO: {completed_count}/{todo_count} | Issues: {issue_count}"
        self.stats_label = BodyLabel(stats_text)
        self.stats_label.setStyleSheet("color: #666;")

        content_layout.addWidget(self.name_label)
        content_layout.addWidget(self.desc_label)
        content_layout.addWidget(self.stats_label)

        # 右侧操作按钮区域
        button_layout = QVBoxLayout()
        button_layout.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignRight)

        # 删除按钮
        self.delete_button = ToolButton(FIF.DELETE)
        self.delete_button.setFixedSize(32, 32)
        self.delete_button.setToolTip("删除项目")
        self.delete_button.clicked.connect(self.on_delete_clicked)

        button_layout.addWidget(self.delete_button)
        button_layout.addStretch()

        # 添加到主布局
        main_layout.addLayout(content_layout, 1)  # 内容区域占据大部分空间
        main_layout.addLayout(button_layout)

        # 设置点击事件
        self.setCursor(Qt.CursorShape.PointingHandCursor)
        
    def on_delete_clicked(self):
        """删除按钮点击事件"""
        self.delete_requested.emit(self.project.id)

    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 检查点击位置是否在删除按钮上
            if not self.delete_button.geometry().contains(event.pos()):
                self.clicked.emit(self.project.id)
        super().mousePressEvent(event)

    def mouseReleaseEvent(self, event):
        """重写鼠标释放事件以避免信号冲突"""
        # 不调用父类的mouseReleaseEvent以避免信号冲突
        pass

class AddProjectDialog(MessageBox):
    """添加项目对话框"""
    
    def __init__(self, parent=None):
        super().__init__("添加新项目", "请输入项目信息", parent)
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        # 项目名称输入
        self.name_edit = LineEdit()
        self.name_edit.setPlaceholderText("项目名称")
        
        # 项目描述输入
        self.desc_edit = TextEdit()
        self.desc_edit.setPlaceholderText("项目描述（可选）")
        self.desc_edit.setFixedHeight(80)
        
        # 添加到对话框
        self.textLayout.addWidget(QLabel("项目名称:"))
        self.textLayout.addWidget(self.name_edit)
        self.textLayout.addWidget(QLabel("项目描述:"))
        self.textLayout.addWidget(self.desc_edit)
        
    def get_project_info(self):
        """获取项目信息"""
        return self.name_edit.text().strip(), self.desc_edit.toPlainText().strip()

class ProjectListWidget(QWidget):
    """项目列表页面"""
    project_selected = pyqtSignal(str)  # 发送选中的项目ID
    project_delete_requested = pyqtSignal(str)  # 发送要删除的项目ID

    def __init__(self, parent=None):
        super().__init__(parent)
        self.projects: List[Project] = []
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 10, 20, 20)  # 与详情页面保持一致的顶部边距
        layout.setSpacing(10)  # 设置组件间距

        # 标题和添加按钮
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)  # 移除header内边距
        header_layout.setSpacing(15)  # 设置header内组件间距

        self.title_label = TitleLabel("项目管理")
        self.add_button = PrimaryPushButton(FIF.ADD, "添加项目")
        self.add_button.clicked.connect(self.add_project)
        self.add_button.setFixedHeight(32)  # 与详情页面按钮高度保持一致

        header_layout.addWidget(self.title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.add_button)

        # 项目列表滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        self.scroll_widget = QWidget()
        self.scroll_layout = QVBoxLayout(self.scroll_widget)
        self.scroll_layout.setSpacing(12)
        self.scroll_layout.setContentsMargins(0, 0, 0, 0)  # 移除滚动区域内边距
        self.scroll_layout.addStretch()

        self.scroll_area.setWidget(self.scroll_widget)

        layout.addLayout(header_layout)
        layout.addWidget(self.scroll_area, 1)  # 给滚动区域设置拉伸因子
        
    def set_projects(self, projects: List[Project]):
        """设置项目列表"""
        self.projects = projects
        self.refresh_projects()
        
    def refresh_projects(self):
        """刷新项目列表显示"""
        # 清除现有项目卡片
        for i in reversed(range(self.scroll_layout.count())):
            item = self.scroll_layout.itemAt(i)
            if item.widget() and isinstance(item.widget(), ProjectCard):
                item.widget().deleteLater()

        # 添加项目卡片
        for project in self.projects:
            card = ProjectCard(project)
            card.clicked.connect(self.project_selected.emit)
            card.delete_requested.connect(self.delete_project)
            self.scroll_layout.insertWidget(self.scroll_layout.count() - 1, card)
            
    def add_project(self):
        """添加项目"""
        dialog = AddProjectDialog(self)
        if dialog.exec():
            name, description = dialog.get_project_info()
            if name:
                # 发送添加项目信号给主窗口处理
                self._find_main_window().add_new_project(name, description)
            else:
                MessageBox("错误", "项目名称不能为空", self).exec()

    def delete_project(self, project_id: str):
        """删除项目"""
        # 找到要删除的项目
        project_to_delete = None
        for project in self.projects:
            if project.id == project_id:
                project_to_delete = project
                break

        if project_to_delete:
            # 显示确认对话框
            title = "确认删除项目"
            content = f"确定要删除项目 '{project_to_delete.name}' 吗？\n\n此操作将删除项目中的所有TODO和Issue，且无法撤销。"
            dialog = MessageBox(title, content, self)

            if dialog.exec():
                # 发送删除项目信号给主窗口处理
                self._find_main_window().delete_project(project_id)

    def _find_main_window(self):
        """查找主窗口"""
        parent = self.parent()
        while parent:
            if hasattr(parent, 'add_new_project') and hasattr(parent, 'delete_project'):
                return parent
            parent = parent.parent()
        return None
