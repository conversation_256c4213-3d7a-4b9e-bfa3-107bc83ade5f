# coding:utf-8
"""
创建包含长标题的测试数据
用于验证文本截断和悬停提示功能
"""

from data_manager import DataManager

def create_long_title_test_data():
    """创建包含长标题的测试数据"""
    print("创建长标题测试数据...")
    
    dm = DataManager()
    
    # 添加一个包含长标题的项目
    long_project = dm.add_project(
        "企业级微服务架构重构项目 - 包含用户认证、数据分析、支付系统等多个子系统的全面升级改造",
        "这是一个大型的企业级系统重构项目，涉及多个业务模块的架构升级，包括但不限于：用户认证系统的现代化改造、数据分析平台的性能优化、支付系统的安全加固、API网关的部署、微服务治理、监控告警系统的建设等多个方面的工作内容"
    )
    
    # 添加长标题的TODO项
    long_todos = [
        ("重构用户认证系统，实现JWT令牌机制、OAuth2.0集成、多因子认证、单点登录等现代化认证功能", 
         "需要完全重写现有的用户认证模块，引入现代化的认证机制，包括JWT令牌的生成和验证、OAuth2.0第三方登录集成、短信和邮箱验证码、Google Authenticator等多因子认证、企业级单点登录SSO、密码策略管理、会话管理、权限控制等功能"),
        
        ("开发实时数据分析仪表板，支持多维度数据展示、自定义报表生成、数据导出等功能", 
         "构建一个功能强大的数据分析平台，支持实时数据处理、多种图表类型展示、用户自定义报表配置、数据钻取分析、定时报表生成、数据导出为Excel/PDF格式、数据权限控制、报表分享等功能"),
        
        ("集成多种第三方支付接口，包括支付宝、微信支付、银联、PayPal等国内外主流支付方式", 
         "实现统一的支付网关，集成支付宝、微信支付、银联在线、PayPal、Stripe等多种支付方式，支持扫码支付、H5支付、APP支付、网页支付等多种支付场景，包含订单管理、支付回调处理、退款功能、对账系统、风控机制等完整的支付流程"),
        
        ("优化数据库性能，实施分库分表策略、读写分离、缓存机制等性能提升方案", 
         "全面优化数据库架构和性能，包括MySQL主从复制配置、读写分离实现、Redis缓存策略设计、数据库连接池优化、SQL查询优化、索引设计优化、分库分表方案实施、数据迁移策略、备份恢复机制等"),
        
        ("建设完整的DevOps流水线，实现自动化构建、测试、部署和监控", 
         "构建现代化的DevOps体系，包括Git代码管理、Jenkins/GitLab CI持续集成、Docker容器化部署、Kubernetes集群管理、自动化测试框架、代码质量检查、安全扫描、性能测试、监控告警、日志收集分析等完整的开发运维流程")
    ]
    
    for title, desc in long_todos:
        long_project.add_todo(title, desc)
    
    # 添加长标题的Issue项
    long_issues = [
        ("移动端应用在不同设备和操作系统版本上的兼容性问题，包括界面适配、功能异常、性能问题等", 
         "在iOS和Android不同版本的设备上测试发现多个兼容性问题：iPhone X以上机型的刘海屏适配问题、Android 11以上版本的权限管理变化导致的功能异常、不同屏幕分辨率下的界面布局错乱、低端设备上的性能问题等"),
        
        ("高并发场景下系统性能瓶颈问题，包括响应时间过长、内存泄漏、数据库连接池耗尽等", 
         "在压力测试中发现当并发用户数超过5000时系统出现严重性能问题：API响应时间从平均200ms增加到3秒以上、内存使用率持续上升导致频繁GC、数据库连接池耗尽导致新请求失败、Redis缓存命中率下降等问题"),
        
        ("第三方API集成稳定性问题，包括接口超时、数据格式变化、服务不可用等异常情况", 
         "集成的多个第三方服务存在稳定性问题：支付接口偶发性超时导致订单状态异常、地图API返回数据格式变化导致解析失败、短信服务商接口限流导致验证码发送失败、天气API服务中断影响相关功能等"),
        
        ("数据安全和隐私保护问题，包括敏感信息泄露、权限控制缺陷、数据传输安全等", 
         "安全审计发现多个安全隐患：用户密码使用MD5加密存在安全风险、API接口缺少权限验证、敏感数据传输未使用HTTPS、日志文件包含用户敏感信息、数据库访问权限过于宽泛、缺少SQL注入防护等问题"),
        
        ("系统监控和告警机制不完善，无法及时发现和处理系统异常和故障", 
         "现有监控系统存在多个不足：缺少业务指标监控、告警规则不够精确导致误报和漏报、日志收集不全面、性能指标监控粒度不够、故障定位困难、缺少自动化恢复机制、监控数据保留时间过短等问题")
    ]
    
    for title, desc in long_issues:
        long_project.add_issue(title, desc)
    
    # 更新项目
    dm.update_project(long_project)
    
    print(f"✅ 已创建包含长标题的测试项目")
    print(f"   项目名称: {long_project.name[:50]}...")
    print(f"   TODO数量: {len(long_project.todos)}")
    print(f"   Issue数量: {len(long_project.issues)}")
    print()
    print("现在可以运行应用程序查看文本截断效果：")
    print("python main.py")

if __name__ == "__main__":
    create_long_title_test_data()
