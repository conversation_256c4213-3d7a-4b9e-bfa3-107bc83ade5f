# coding:utf-8
"""
文本截断功能演示脚本
展示长标题的智能截断和悬停提示功能
"""

from text_utils import get_display_text_and_tooltip

def demo_text_truncation_feature():
    """演示文本截断功能"""
    print("🎯 文本截断功能演示")
    print("=" * 60)
    
    print("📝 功能说明：")
    print("   当项目名称、TODO标题、Issue标题过长时，系统会自动截断显示")
    print("   并在末尾添加省略号(...)，鼠标悬停时显示完整内容")
    print()
    
    # 演示不同组件的截断规则
    print("📏 截断规则：")
    print("   • 项目名称：最大30字符")
    print("   • 项目描述：最大60字符") 
    print("   • TODO标题：最大25字符")
    print("   • Issue标题：最大25字符")
    print("   • TODO/Issue描述：最大40字符")
    print("   • 项目详情页标题：最大40字符")
    print()
    
    # 演示实际效果
    print("🎬 效果演示：")
    print()
    
    # 项目名称演示
    print("1️⃣ 项目名称截断效果：")
    project_names = [
        "短项目名",
        "中等长度的项目名称",
        "这是一个比较长的项目名称，用来测试截断效果",
        "企业级微服务架构重构项目 - 包含用户认证、数据分析、支付系统等多个子系统的全面升级改造"
    ]
    
    for i, name in enumerate(project_names, 1):
        display, tooltip = get_display_text_and_tooltip(name, 30)
        print(f"   项目{i}: {name}")
        print(f"   显示: {display}")
        print(f"   提示: {'有完整内容提示' if tooltip else '无需提示'}")
        print()
    
    # TODO标题演示
    print("2️⃣ TODO标题截断效果：")
    todo_titles = [
        "简单任务",
        "完成用户界面设计",
        "实现用户认证系统的登录注册功能",
        "重构用户认证系统，实现JWT令牌机制、OAuth2.0集成、多因子认证、单点登录等现代化认证功能"
    ]
    
    for i, title in enumerate(todo_titles, 1):
        display, tooltip = get_display_text_and_tooltip(title, 25)
        print(f"   TODO{i}: {title}")
        print(f"   显示: {display}")
        print(f"   提示: {'有完整内容提示' if tooltip else '无需提示'}")
        print()
    
    # Issue标题演示
    print("3️⃣ Issue标题截断效果：")
    issue_titles = [
        "登录Bug",
        "移动端界面适配问题",
        "系统在高并发情况下的性能问题",
        "移动端应用在不同设备和操作系统版本上的兼容性问题，包括界面适配、功能异常、性能问题等"
    ]
    
    for i, title in enumerate(issue_titles, 1):
        display, tooltip = get_display_text_and_tooltip(title, 25)
        print(f"   Issue{i}: {title}")
        print(f"   显示: {display}")
        print(f"   提示: {'有完整内容提示' if tooltip else '无需提示'}")
        print()
    
    # 描述截断演示
    print("4️⃣ 描述文本截断效果：")
    descriptions = [
        "简短描述",
        "这是一个中等长度的描述文本",
        "这是一个比较详细的描述，包含了更多的信息内容",
        "这是一个非常详细的描述文本，包含了大量的信息来说明具体的需求、实现方案、技术细节、注意事项等各个方面的内容"
    ]
    
    for i, desc in enumerate(descriptions, 1):
        display, tooltip = get_display_text_and_tooltip(desc, 40)
        print(f"   描述{i}: {desc}")
        print(f"   显示: {display}")
        print(f"   提示: {'有完整内容提示' if tooltip else '无需提示'}")
        print()
    
    # 使用指南
    print("📖 使用指南：")
    print("   1. 启动应用：python main.py")
    print("   2. 查看项目列表中的长标题截断效果")
    print("   3. 将鼠标悬停在截断的文本上查看完整内容")
    print("   4. 点击进入项目详情页面")
    print("   5. 查看TODO和Issue列表中的截断效果")
    print("   6. 测试悬停提示功能")
    print()
    
    # 技术特点
    print("🔧 技术特点：")
    print("   • 智能截断：根据组件类型使用不同的最大长度")
    print("   • 优雅省略：使用...表示文本被截断")
    print("   • 悬停提示：鼠标悬停显示完整内容")
    print("   • 性能优化：只在需要时才显示提示")
    print("   • 用户友好：保持界面整洁的同时不丢失信息")
    print()
    
    # 应用场景
    print("🎯 应用场景：")
    print("   • 项目名称过长时的显示优化")
    print("   • TODO任务标题的整洁显示")
    print("   • Issue问题描述的简洁展示")
    print("   • 列表界面的空间优化")
    print("   • 提升用户体验和界面美观度")
    print()
    
    print("✨ 现在可以运行应用程序体验文本截断功能！")
    print("   python main.py")
    print()
    print("💡 提示：可以运行以下命令创建包含长标题的测试数据：")
    print("   python create_long_title_data.py")

if __name__ == "__main__":
    demo_text_truncation_feature()
